import cloudinary from 'cloudinary'

import { CLOUD_API_KEY, CLOUD_NAME } from '@/shared-config/envs'

cloudinary.v2.config({
  cloud_name: CLOUD_NAME,
  api_key: CLOUD_API_KEY,
  api_secret: process.env.CLOUD_API_SECRET,
})

export async function destroyCloudinaryResource(id: string, resource_type: string) {
  return await cloudinary.v2.uploader.destroy(id, {
    resource_type: resource_type,
  })
}
