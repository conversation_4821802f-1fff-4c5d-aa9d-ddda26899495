import { USER_ROLE_ENUM, USER_STATUS_ENUM } from '@memberup/shared/src/types/enum'

import 'next-auth'

declare module 'next-auth' {
  interface Session {
    user: {
      /**
       * The user's email address
       */
      email?: string | null

      /**
       * The user's unique id number
       */
      id?: string | null

      /**
       * The users preferred avatar.
       * Usually provided by the user's OAuth provider of choice
       */
      image?: string | null

      /**
       * The user's full name
       */
      name?: string | null

      /**
       * The user's first name
       */
      first_name?: string | null

      /**
       * The user's last name
       */
      last_name?: string | null

      /**
       * The user's custom & public [username] viewable to others
       */

      phone_number?: string

      membership?: string

      role?: USER_ROLE_ENUM

      status?: USER_STATUS_ENUM
    }
  }

  interface User {
    /**
     * The user's email address
     */
    email?: string | null

    /**
     * The user's unique id number
     */
    id?: string | null

    /**
     * The users preferred avatar.
     * Usually provided by the user's OAuth provider of choice
     */
    image?: string | null

    /**
     * The user's full name
     */
    name?: string | null

    /**
     * The user's first name
     */
    first_name?: string | null

    /**
     * The user's last name
     */
    last_name?: string | null

    /**
     * The user's custom & public [username] viewable to others
     */

    phone_number?: string

    membership?: string

    role?: USER_ROLE_ENUM

    status?: USER_STATUS_ENUM
  }
}
