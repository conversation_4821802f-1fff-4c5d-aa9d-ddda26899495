import { Box } from '@mui/material'
import React from 'react'

import useAppTheme from '@/memberup/components/hooks/use-app-theme'

export default function MBTIBadge({ mbtiType }: { mbtiType: string }) {
  const { isDarkTheme } = useAppTheme()

  return (
    <Box
      sx={{
        lineHeight: '24px',
        p: '0 8px',
        borderRadius: '12px',
        width: 'fit-content',
        display: 'inline-block',
        verticalAlign: 'middle',
        userSelect: 'none',
        color: 'var(--primary-100)',
        backgroundColor: isDarkTheme ? 'var(--black-1100)' : 'var(--font-light-ui-gray)',
        '& span': {
          fontSize: '12px',
          fontWeight: 500,
        },
      }}
    >
      <Box
        sx={{
          width: '8px',
          height: '8px',
          borderRadius: '4px',
          backgroundColor: 'var(--primary-100)',
          display: 'inline-block',
          mr: '4px',
        }}
      />
      <span className="font-family-graphik-medium">{mbtiType}</span>
    </Box>
  )
}
