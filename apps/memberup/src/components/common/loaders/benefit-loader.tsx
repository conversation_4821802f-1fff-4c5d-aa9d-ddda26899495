import Skeleton from '@mui/material/Skeleton'
import Stack from '@mui/material/Stack'
import React from 'react'

const BenefitLoader: React.FC<{
  animation?: 'pulse' | 'wave' | false
  foregroundColor?: string
  backgroundColor?: string
}> = ({ animation, foregroundColor, backgroundColor }) => {
  return (
    <div className="w-100 h-100">
      <Stack className="h-100" direction="row" spacing={3}>
        <Skeleton variant="rounded" width={56} height={56} />
        <Stack spacing={2} flex={1}>
          <Skeleton variant="rounded" height={16} />
          <Skeleton variant="rounded" height={12} />
          <Skeleton variant="rounded" height={12} />
        </Stack>
      </Stack>
    </div>
  )
}

export default React.memo(BenefitLoader)
