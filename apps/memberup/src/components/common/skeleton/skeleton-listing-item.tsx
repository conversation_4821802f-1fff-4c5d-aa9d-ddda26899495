import { Box } from '@mui/material'
import { styled } from '@mui/material/styles'

const SkeletonElement = styled(Box)(({ theme }) => ({
  borderRadius: '12px',
  background: theme.palette.mode === 'dark' ? 'var(--dark-background-2)' : 'var(--ui-dark-300)',
}))

export default function SkeletonListingItem() {
  return (
    <Box sx={{ display: 'flex' }}>
      <SkeletonElement sx={{ width: '64px', height: '64px', mr: '16px' }} />
      <Box>
        <SkeletonElement sx={{ width: '172px', height: '15px', mt: '11px' }} />
        <SkeletonElement sx={{ width: '111px', height: '15px', mt: '11px' }} />
      </Box>
    </Box>
  )
}
