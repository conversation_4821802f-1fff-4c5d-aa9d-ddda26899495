import { PublicOutlined } from '@mui/icons-material'
import { IconButton, IconButtonProps } from '@mui/material'
import { styled } from '@mui/material/styles'
import { Fragment } from 'react'

import FacebookIcon from '../icons/facebook'
import InstagramIcon from '../icons/instagram'
import LinkedInIcon from '../icons/linkedin'
import TikTokIcon from '../icons/tiktok'
import XIcon from '../icons/x'
import YouTubeIcon from '../icons/youtube'
import { THEME_MODE_ENUM } from '@memberup/shared/src/types/enum'

// @TODO: solve typing issue: href prop not recognized on styled component
const SocialLink = styled(IconButton)<IconButtonProps & { href: string }>(({ theme }) => ({
  fontSize: '24px',
  marginRight: '3px',
  cursor: 'pointer',
  color: theme.palette.mode === THEME_MODE_ENUM.dark ? 'var(--ui-dark-1000)' : 'var(--ui-light-1000)',
}))

export default function SocialLinks({
  website,
  facebook,
  instagram,
  youtube,
  x,
  tiktok,
}: {
  website?: string
  facebook?: string
  instagram?: string
  youtube?: string
  x?: string
  tiktok?: string
}) {
  if (!website && !facebook && !instagram && !youtube && !x && !tiktok) return null

  return (
    <Fragment>
      {website && (
        <SocialLink href={website}>
          <PublicOutlined />
        </SocialLink>
      )}
      {facebook && (
        <SocialLink href={`https://facebook.com/${facebook}`}>
          <FacebookIcon />
        </SocialLink>
      )}
      {instagram && (
        <SocialLink href={`https://instagram.com/${instagram}`}>
          <InstagramIcon />
        </SocialLink>
      )}
      <SocialLink href={`https://www.linkedin.com/profile`}>
        <LinkedInIcon />
      </SocialLink>
      {youtube && (
        <SocialLink href={`https://youtube.com/${youtube}`}>
          <YouTubeIcon />
        </SocialLink>
      )}
      {x && (
        <SocialLink href={`https://x.com/${x}`}>
          <XIcon />
        </SocialLink>
      )}
      {tiktok && (
        <SocialLink href={`https://tiktok.com/${tiktok}`}>
          <TikTokIcon />
        </SocialLink>
      )}
    </Fragment>
  )
}
