import useTheme from '@mui/material/styles/useTheme'
import TextField from '@mui/material/TextField'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import React from 'react'

const CustomDatePicker: React.FC<{
  value
  label: string
  handleChange: (e) => void
  [x: string]: any
}> = ({ value, label, handleChange, ...rest }) => {
  const theme = useTheme()

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <DatePicker
        label={label}
        value={value}
        PopperProps={{
          sx: {
            '& .MuiPickersCalendarHeader-label': {
              color: theme.palette.text.primary,
            },
            '& .PrivatePickersYear-yearButton': {
              color: theme.palette.text.primary,
            },
          },
        }}
        onChange={(newValue) => {
          handleChange(newValue)
        }}
        renderInput={(params) => (
          <TextField
            size="small"
            {...(params as any)}
            disabled={false}
            InputProps={{ ...(params.InputProps || {}), readOnly: false }}
            inputProps={{ ...(params.inputProps || {}) }}
          />
        )}
        {...rest}
      />
    </LocalizationProvider>
  )
}

export default CustomDatePicker
