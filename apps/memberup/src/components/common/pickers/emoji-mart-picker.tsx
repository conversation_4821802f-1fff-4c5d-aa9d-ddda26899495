import originalData from '@emoji-mart/data/sets/14/facebook.json'
import Picker from '@emoji-mart/react'

export default function EmojiMartPicker({ onEmojiSelect, onPickerClose, theme = 'light' }) {
  let data = JSON.parse(JSON.stringify(originalData))
  let emojisToRemove = ['relaxed']

  for (let emoji of emojisToRemove) {
    delete data.emojis[emoji]
  }

  return (
    <Picker
      theme={theme}
      data={data}
      onEmojiSelect={onEmojiSelect}
      set="facebook"
      skin="1"
      autoFocus
      onClickOutside={onPickerClose}
    />
  )
}
