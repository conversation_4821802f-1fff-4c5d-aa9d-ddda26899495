import Button from '@mui/material/Button'
import CircularProgress from '@mui/material/CircularProgress'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import Stack from '@mui/material/Stack'
import useTheme from '@mui/material/styles/useTheme'
import { makeStyles } from '@mui/styles'
import React from 'react'

import { adjustRGBA } from '@memberup/shared/src/libs/color'
import SVGClose from '@/memberup/components/svgs/close'

const useStyles = makeStyles((theme) => ({
  root: {
    '& .MuiDialog-paper': {
      borderRadius: 12,
    },
    '& .MuiDialogTitle-root .MuiTypography-h6': {
      fontSize: 16,
    },
    padding: '24px',
  },
  dialogTitle: {
    borderBottom: 'none',
    padding: 24,
    paddingRight: 48,
    textAlign: 'center',
  },
  dialogContent: {
    padding: '24px !important',
    textAlign: 'center',
  },
}))

const AppWarning: React.FC<{
  title?: React.ReactNode
  content: React.ReactNode
  cancelButtonText: string
  proceedButtonText: React.ReactNode
  open: boolean
  onProceed: () => void
  onClose: () => void
  disabled?: boolean
  showCloseButton?: boolean
  showCancelButton?: boolean
  showTitle?: boolean
  isLoading?: boolean
  sx?: any
}> = ({
  title,
  content,
  cancelButtonText,
  proceedButtonText,
  open,
  onProceed,
  onClose,
  disabled = false,
  showCloseButton = true,
  showCancelButton = true,
  showTitle = true,
  sx = {},
  isLoading = false,
}) => {
  const classes = useStyles()
  const theme = useTheme()

  return (
    <Dialog
      maxWidth="xs"
      fullWidth={true}
      className={classes.root}
      open={open}
      sx={sx}
      onClose={() => onClose()}
      PaperProps={{
        onClick: (e) => e.stopPropagation(),
      }}
      aria-labelledby="app-warning-dialog-title"
    >
      {showTitle && (
        <DialogTitle className={classes.dialogTitle} id="app-warning-dialog-title">
          {title}
          {showCloseButton && (
            <IconButton
              size="small"
              aria-label="close"
              className="close color03"
              onClick={(e) => {
                e.preventDefault()
                e.stopPropagation()
                onClose()
              }}
            >
              <SVGClose fontSize={16} />
            </IconButton>
          )}
        </DialogTitle>
      )}
      <DialogContent className={classes.dialogContent}>
        <Grid container>
          <Grid xs={12} sx={{ mb: '32px' }}>
            {content}
          </Grid>
        </Grid>
        <Stack direction={'row'}>
          {showCancelButton && (
            <Button
              disabled={disabled}
              className="round-small cancel-button"
              variant="outlined"
              color="inherit"
              fullWidth
              onClick={(e) => {
                e.preventDefault()
                e.stopPropagation()
                onClose()
              }}
              data-testid="warning-cancel-button"
            >
              {cancelButtonText}
            </Button>
          )}
          <Button
            disabled={disabled || isLoading}
            className="round-small proceed-button"
            sx={{ backgroundColor: adjustRGBA(theme.palette.primary.main, 0.8), color: '#fff' }}
            variant="contained"
            color="primary"
            fullWidth
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
              onProceed()
            }}
            data-testid="warning-proceed-button"
          >
            {isLoading && <CircularProgress size={16} />}
            {proceedButtonText}
          </Button>
        </Stack>
      </DialogContent>
    </Dialog>
  )
}

export default React.memo(AppWarning)
