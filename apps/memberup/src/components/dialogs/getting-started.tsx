//@ts-nocheck
import InfoIcon from '@mui/icons-material/Info'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import CircularProgress from '@mui/material/CircularProgress'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import Grid from '@mui/material/Grid'
import useTheme from '@mui/material/styles/useTheme'
import Typography from '@mui/material/Typography'
import clsx from 'clsx'
import React, { useState } from 'react'

import { useBrowserLayoutEffect } from '@memberup/shared/src/components/hooks/use-browser-layout-effect'
import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { ThemeTemplate } from '@memberup/shared/src/components/theme/template'
import { hexToRGBA } from '@memberup/shared/src/libs/color'
import { DefaultLightThemeOptions, DefaultThemeOptions } from '@memberup/shared/src/settings/theme'
import { THEME_MODE_ENUM } from '@memberup/shared/src/types/enum'
import {
  selectMembershipSetting,
  selectMembershipTheme,
  selectRequestUpdateMembershipSetting,
  updateMembershipSetting,
  updateMembershipSettingSuccess,
} from '@/memberup/store/features/membershipSlice'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'

const getColorOptions = (themeOptions) => {
  return {
    backgroundColor: themeOptions.palette.background.paper,
    textColorPrimary: themeOptions.palette.text.primary,
    textColorSecondary: themeOptions.palette.text.secondary,
    textColorDisabled: themeOptions.palette.text.disabled,
  }
}

const primaryColorOptions = [
  'rgba(123,81,224,1)',
  'rgba(58,134,255,1)',
  'rgba(88,181,96,1)',
  'rgba(255,84,87,1)',
  'rgba(248,150,30,1)',
  'rgba(255,190,11,1)',
  'rgba(183,149,77,1)',
  'rgba(219,139,231,1)',
]
const secondaryColorOptions = [
  'linear-gradient(90deg, rgba(117,83,216,1) 0%, rgba(181,83,216,1) 50%, rgba(216,83,157,1) 100%)',
  'linear-gradient(90deg, rgba(76,128,239,1) 0%, rgba(88,69,216,1) 50%, rgba(149,69,216,1) 100%)',
  'linear-gradient(90deg, rgba(107,171,101,1) 0%, rgba(74,126,105,1) 50%, rgba(116,169,197,1) 100%)',
  'linear-gradient(90deg, rgba(224,92,88,1) 0%, rgba(168,99,66,1) 50%, rgba(152,115,60,1) 100%)',
  'linear-gradient(90deg, rgba(226,150,61,1) 0%, rgba(215,177,58,1) 50%, rgba(224,219,60,1) 100%)',
  'linear-gradient(90deg, rgba(233,183,65,1) 0%, rgba(226,211,63,1) 50%, rgba(160,215,60,1) 100%)',
  'linear-gradient(90deg, rgba(168,142,83,1) 0%, rgba(152,144,75,1) 50%, rgba(142,171,84,1) 100%)',
  'linear-gradient(90deg, rgba(196,135,213,1) 0%, rgba(225,143,185,1) 50%, rgba(233,155,148,1) 100%)',
]

const GettingStartedCommunityIdea: React.FC = () => {
  const mountedRef = useMounted(true)
  const theme = useTheme()
  const dispatch = useAppDispatch()
  const membershipSetting = useAppSelector((state) => selectMembershipSetting(state))
  const reuqestUpdateMembershipSetting = useAppSelector((state) => selectRequestUpdateMembershipSetting(state))
  const [communityIdea, setCommunityIdea] = useState(null)
  const activeThemeMode = membershipSetting.theme_mode || THEME_MODE_ENUM.dark

  const themeOptions = activeThemeMode === THEME_MODE_ENUM.dark ? DefaultThemeOptions : DefaultLightThemeOptions

  const colorOptions = getColorOptions(themeOptions)

  useBrowserLayoutEffect(() => {
    if (!mountedRef.current) return
    setCommunityIdea(membershipSetting?.community_idea)
  }, [membershipSetting])

  const handleSubmit = () => {
    dispatch(
      updateMembershipSetting({
        data: {
          community_idea: communityIdea,
          completed_membership: 3,
        },
        noToast: true,
      }),
    )
  }

  const ideaItemTitleSx = {
    color: colorOptions.textColorPrimary,
    fontSize: '18px',
    fontWeight: 700,
    lineHeight: '24px',
  }
  const ideaItemTitleDescSx = {
    color: colorOptions.textColorPrimary,
    fontSize: '14px',
    letterSpacing: '-0.5px',
    lineHeight: '20px',
    mt: 2,
  }
  const ideaItemDescSx = {
    color: colorOptions.textColorDisabled,
    fontSize: '13px',
    lineHeight: '20px',
    minHeight: 50,
  }
  const communityIdeas = [
    {
      value: true,
      content: (
        <>
          <Grid container spacing={2}>
            <Grid item>
              <Typography sx={ideaItemTitleSx}>🚀</Typography>
            </Grid>
            <Grid item xs>
              <Typography sx={ideaItemTitleSx}>Yes</Typography>
            </Grid>
          </Grid>
          <Typography
            //className="idea-title-desc"
            variant="body1"
            color="text.disabled"
            sx={ideaItemTitleDescSx}
          >
            We love that for you 👏
          </Typography>
        </>
      ),
      description: (
        <Grid container spacing={2}>
          <Grid className="text-right" item>
            <InfoIcon color="disabled" />
          </Grid>
          <Grid item xs>
            <Typography variant="body1" sx={ideaItemDescSx}>
              We believe in providing the best education - for free. When you click the button below, you&rsquo;ll
              instantly be granted access to a full course taught by industry leading experts who&rsquo;ve sold over
              $50M with their communities.
            </Typography>
          </Grid>
        </Grid>
      ),
    },
    {
      value: false,
      content: (
        <>
          <Grid container spacing={2}>
            <Grid item>
              <Typography sx={ideaItemTitleSx}>🤔</Typography>
            </Grid>
            <Grid item xs>
              <Typography sx={ideaItemTitleSx}>No</Typography>
            </Grid>
          </Grid>
          <Typography
            //className="idea-title-desc"
            variant="body1"
            color="text.disabled"
            sx={ideaItemTitleDescSx}
          >
            No worries, we got your back 😎
          </Typography>
        </>
      ),
      description: (
        <Grid container spacing={2}>
          <Grid className="text-right" item>
            <InfoIcon color="disabled" />
          </Grid>
          <Grid item xs>
            <Typography variant="body1" color="text.disabled" sx={ideaItemDescSx}>
              We believe in providing the best education - for free. When you click the button below, you&rsquo;ll
              instantly be granted access to a full course taught by industry leading experts who&rsquo;ve sold over
              $50M with their communities.
            </Typography>
          </Grid>
        </Grid>
      ),
    },
  ]

  return (
    <>
      <Typography variant="h4" align="center" color="inherit" sx={{ mb: '16px' }}>
        Do you know your community idea?
      </Typography>
      <Typography variant="body2" align="center" sx={{ color: colorOptions.textColorDisabled, mb: '32px' }}>
        This isn&rsquo;t school, there are no wrong answers here&nbsp;
        <span style={{ color: colorOptions.textColorPrimary }}>&#128522;</span>
      </Typography>
      <Box sx={{ p: 2 }}>
        <Grid container spacing={2}>
          {communityIdeas.map((item, index) => (
            <Grid key={`community-idea-${index}`} item xs={6}>
              <Box
                sx={{
                  backgroundColor: communityIdea === item.value ? colorOptions.backgroundColor : 'unset',
                  border: '1px solid',
                  borderColor:
                    communityIdea === item.value ? colorOptions.textColorPrimary : DefaultThemeOptions.palette.divider,
                  cursor: 'pointer',
                  margin: 'auto',
                  height: 150,
                  borderRadius: '12px',
                  px: '24px',
                  pt: '48px',
                  '&:hover': {
                    borderColor: colorOptions.textColorPrimary,
                  },
                  '& .idea-title-desc': {
                    color: communityIdea === item.value ? `${colorOptions.textColorSecondary}!important` : undefined,
                  },
                }}
                onClick={() => setCommunityIdea(item.value)}
                data-cy="gt-c-idea-option"
              >
                {item.content}
              </Box>
            </Grid>
          ))}
        </Grid>
      </Box>
      <Box sx={{ mb: '48px', pt: '16px' }}>
        {communityIdea === true && communityIdeas[0].description}
        {communityIdea === false && communityIdeas[1].description}
      </Box>
      <Button
        className="round-small"
        disabled={reuqestUpdateMembershipSetting}
        fullWidth
        variant="contained"
        onClick={handleSubmit}
        data-cy="gt-c-idea-submit-button"
        sx={{ color: colorOptions.textColorPrimary, py: '18px', borderRadius: '10px !important' }}
      >
        {reuqestUpdateMembershipSetting ? <CircularProgress size={16} /> : <b>Awesome, give it to me!</b>}
      </Button>
    </>
  )
}

const GettingStartedTheme: React.FC = () => {
  const mountedRef = useMounted(true)
  const dispatch = useAppDispatch()
  const membershipSetting = useAppSelector((state) => selectMembershipSetting(state))
  const membershipTheme = useAppSelector((state) => selectMembershipTheme(state))
  const reuqestUpdateMembershipSetting = useAppSelector((state) => selectRequestUpdateMembershipSetting(state))
  const activePrimaryColor = hexToRGBA(membershipTheme.palette.primary?.['dark'])
  const activeGradientColor =
    membershipTheme.components.MuiCssBaseline?.styleOverrides?.['body']?.['& .background-gradient01']?.background
  const activeThemeMode = membershipSetting.theme_mode || THEME_MODE_ENUM.dark

  const themeOptions = activeThemeMode === THEME_MODE_ENUM.dark ? DefaultThemeOptions : DefaultLightThemeOptions

  const colorOptions = getColorOptions(themeOptions)

  const handleSubmit = () => {
    dispatch(
      updateMembershipSetting({
        data: {
          theme_main_color: activePrimaryColor,
          theme_secondary_color: activeGradientColor,
          theme_mode: activeThemeMode,
          completed_membership: 2,
        },
        noToast: true,
      }),
    )
  }

  return (
    <>
      <Typography variant="h4" align="center" color="inherit" sx={{ mb: '16px' }}>
        ✨ Customize your look
      </Typography>
      <Typography variant="body2" align="center" sx={{ color: colorOptions.textColorDisabled, mb: '32px' }}>
        Customize your community&apos;s look and feel. Relax, you can change this later.
      </Typography>
      <Box
        sx={{
          backgroundColor: activeThemeMode === THEME_MODE_ENUM.dark ? '#212124' : '#F2F2F2',
          borderRadius: '16px',
          p: '18px',
          mb: '16px',
        }}
      >
        <Grid container spacing={2}>
          {[THEME_MODE_ENUM.dark, THEME_MODE_ENUM.light].map((item) => (
            <Grid key={item} item xs={6}>
              <Box
                sx={{
                  m: 'auto',
                  cursor: 'pointer',
                }}
              >
                <Box
                  sx={{
                    borderColor: activeThemeMode === item ? colorOptions.textColorPrimary : 'transparent',
                    borderRadius: '16px',
                    borderStyle: 'solid',
                    borderWidth: '2px',
                    p: '4px',
                  }}
                  onClick={() => {
                    dispatch(
                      updateMembershipSettingSuccess({
                        data: {
                          theme_mode: item,
                        },
                        partialChanged: true,
                      }),
                    )
                  }}
                  data-cy="gt-theme-theme-option"
                >
                  <Box
                    sx={{
                      borderRadius: '12px',
                    }}
                  >
                    {item === THEME_MODE_ENUM.dark ? (
                      <ThemeTemplate
                        layoutBackgroundColor={DefaultThemeOptions.palette.background.paper}
                        layoutSecondaryColor={membershipSetting.theme_secondary_color}
                        pageBackgroundColor={DefaultThemeOptions.palette.background.paper}
                        textColor={DefaultThemeOptions.palette.text.primary}
                      />
                    ) : (
                      <ThemeTemplate
                        layoutBackgroundColor={DefaultLightThemeOptions.palette.background.paper}
                        layoutSecondaryColor={membershipSetting.theme_secondary_color}
                        pageBackgroundColor={DefaultLightThemeOptions.palette.background.paper}
                        textColor={DefaultLightThemeOptions.palette.text.primary}
                      />
                    )}
                  </Box>
                </Box>
                <Typography
                  variant="subtitle1"
                  color="inherit"
                  sx={{ mt: '8px', pl: '8px', fontWeight: 700, textTransform: 'capitalize' }}
                >
                  {item}
                </Typography>
              </Box>
            </Grid>
          ))}
        </Grid>
      </Box>
      <Box
        sx={{
          backgroundColor: activeThemeMode === THEME_MODE_ENUM.dark ? '#212124' : '#F2F2F2',
          borderRadius: '16px',
          px: '24px',
          py: '20px',
          mb: '16px',
        }}
      >
        <Grid container alignItems="center" spacing={2}>
          <Grid item sx={{ minWidth: 80 }}>
            <Typography variant="body1" color="inherit" sx={{ fontWeight: 700 }}>
              Primary
            </Typography>
          </Grid>
          <Grid item xs>
            <Grid container>
              {primaryColorOptions.map((c, i) => {
                return (
                  <Grid key={`preset-color-${i}`} item xs>
                    <Box
                      className={clsx('preset-color-wrapper', {
                        active: activePrimaryColor === c,
                      })}
                      style={{
                        borderColor: activePrimaryColor === c ? colorOptions.textColorPrimary : undefined,
                      }}
                      onClick={() => {
                        dispatch(
                          updateMembershipSettingSuccess({
                            data: {
                              theme_main_color: c,
                            },
                            partialChanged: true,
                          }),
                        )
                      }}
                      data-cy="gt-theme-primary-color-item"
                    >
                      <span
                        className="preset-color"
                        style={{
                          backgroundColor: c,
                          borderWidth: 0,
                        }}
                      ></span>
                    </Box>
                  </Grid>
                )
              })}
            </Grid>
          </Grid>
        </Grid>
      </Box>
      <Box
        sx={{
          backgroundColor: activeThemeMode === THEME_MODE_ENUM.dark ? '#212124' : '#F2F2F2',
          borderRadius: '16px',
          px: '24px',
          py: '20px',
          mb: '48px',
        }}
      >
        <Grid container alignItems="center" spacing={2}>
          <Grid item sx={{ minWidth: 80 }}>
            <Typography variant="body1" color="inherit" sx={{ fontWeight: 700 }}>
              Secondary
            </Typography>
          </Grid>
          <Grid item xs>
            <Grid container>
              {secondaryColorOptions.map((c, i) => {
                return (
                  <Grid key={`preset-color-${i}`} item xs>
                    <div
                      className={clsx('preset-color-wrapper', {
                        active: activeGradientColor === c,
                      })}
                      style={{
                        borderColor: activeGradientColor === c ? colorOptions.textColorPrimary : undefined,
                      }}
                      onClick={() => {
                        dispatch(
                          updateMembershipSettingSuccess({
                            data: {
                              theme_secondary_color: c,
                            },
                            partialChanged: true,
                          }),
                        )
                      }}
                      data-cy="gt-theme-secondary-color-item"
                    >
                      <span
                        className="preset-color"
                        style={{
                          background: c,
                          borderWidth: 0,
                        }}
                      ></span>
                    </div>
                  </Grid>
                )
              })}
            </Grid>
          </Grid>
        </Grid>
      </Box>
      <Button
        className="round-small"
        disabled={reuqestUpdateMembershipSetting}
        variant="contained"
        fullWidth
        onClick={() => handleSubmit()}
        data-cy="gt-theme-submit-button"
        sx={{ color: colorOptions.textColorPrimary, py: '18px', borderRadius: '10px !important' }}
      >
        {reuqestUpdateMembershipSetting ? <CircularProgress size={16} /> : <b>Continue</b>}
      </Button>
    </>
  )
}

const GettingStarted: React.FC<{ open: boolean }> = ({ open }) => {
  const mountedRef = useMounted(true)
  const membershipSetting = useAppSelector((state) => selectMembershipSetting(state))
  const [completedMembership, setCompletedMembership] = useState(membershipSetting.completed_membership || 1)

  const activeThemeMode = membershipSetting.theme_mode || THEME_MODE_ENUM.dark

  const themeOptions = activeThemeMode === THEME_MODE_ENUM.dark ? DefaultThemeOptions : DefaultLightThemeOptions

  const colorOptions = getColorOptions(themeOptions)

  useBrowserLayoutEffect(() => {
    if (mountedRef.current && membershipSetting?.completed_membership) {
      setCompletedMembership(membershipSetting.completed_membership)
    }
  }, [membershipSetting?.completed_membership])

  const STEPS = {
    1: () => <GettingStartedTheme />,
    2: () => <GettingStartedCommunityIdea />,
  }

  const CurrentStep = STEPS[completedMembership] || (() => null)

  return (
    <Dialog
      fullWidth={true}
      open={open}
      aria-labelledby="assign-member-role-dialog-title"
      sx={{
        '& .MuiDialog-paper': {
          backgroundColor: 'rgba(0,0,0,0)',
          height: '100%',
          width: '100%',
          maxHeight: '100%',
          maxWidth: '100%',
          m: 0,
        },
        '& .MuiBackdrop-root': {
          backgroundColor: 'rgba(75,75,75,0.3)',
          opacity: 1,
        },
      }}
    >
      <DialogContent
        sx={{
          display: 'flex',
          alignItems: 'center',
          position: 'relative',
          maxWidth: 1440,
          m: 'auto',
          p: '32px',
        }}
      >
        <Box
          sx={{
            backgroundColor: colorOptions.backgroundColor,
            borderRadius: '24px',
            color: colorOptions.textColorPrimary,
            width: '100%',
            m: 'auto',
            maxWidth: 640,
            p: '32px',
            pt: '38px',
            pb: '24px',
          }}
        >
          <CurrentStep />
        </Box>
      </DialogContent>
    </Dialog>
  )
}

GettingStarted.displayName = 'GettingStarted'

export default GettingStarted
