import CloseIcon from '@mui/icons-material/Close'
import { Box, useMediaQuery } from '@mui/material'
import Chip from '@mui/material/Chip'
import Dialog from '@mui/material/Dialog'
import IconButton from '@mui/material/IconButton'
import Stack from '@mui/material/Stack'
import useTheme from '@mui/material/styles/useTheme'
import { makeStyles } from '@mui/styles'
import Image from 'next/image'
import { useEffect, useReducer } from 'react'

import SVGChevronRight from '../../svgs/chevron-right'
import { AppVideo } from '@memberup/shared/src/components/common/media/video'
import { ChevronLeft24Icon, Photos20Icon } from '@/components/icons'
import { GiphyGif } from '@/components/images/giphy-gif'

const useStyles = makeStyles({
  mediaGallery: {
    display: 'flex',
    alignItems: 'center',
    margin: '0 auto',
    backgroundColor: '#000000',
  },
  media: {
    margin: 'auto',
    display: 'block',
    height: 'inherit',
    objectFit: 'scale-down',
    flexGrow: 1,
    borderRadius: '12px',
    maxWidth: '100%',
  },
  topNav: {
    alignItems: 'center',
    position: 'absolute',
    right: '10px',
    top: '10px',
  },
})

const FullScreenMediaViewer = ({ open, selectedMediaIndex, medias, handleClose }) => {
  const classes = useStyles()
  const NAVIGATE_NEXT = 'NAVIGATE_NEXT'
  const NAVIGATE_PREVIOUS = 'NAVIGATE_PREVIOUS'

  const mediaGalleryReducer = (state, action) => {
    let updatedIndex = state.selectedMediaIndex
    switch (action.type) {
      case NAVIGATE_NEXT:
        if (state.canNavigateNext) {
          updatedIndex = state.selectedMediaIndex + 1
        }
        break
      case NAVIGATE_PREVIOUS:
        if (state.canNavigatePrevious) {
          updatedIndex = state.selectedMediaIndex - 1
        }
        break
      default:
        return state
    }
    return {
      ...state,
      selectedMediaIndex: updatedIndex,
      canNavigateNext: updatedIndex < medias.length - 1,
      canNavigatePrevious: updatedIndex > 0,
      currentMedia: medias[updatedIndex],
    }
  }

  const initialMediaIndex = selectedMediaIndex >= 0 && selectedMediaIndex <= medias.length - 1 ? selectedMediaIndex : 0
  const initialState = {
    medias,
    selectedMediaIndex: initialMediaIndex,
    canNavigateNext: initialMediaIndex < medias.length - 1,
    canNavigatePrevious: initialMediaIndex > 0,
    currentMedia: medias[initialMediaIndex],
  }

  const [state, dispatch] = useReducer(mediaGalleryReducer, initialState)
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'))
  useEffect(() => {
    const handleKeyDown = (event) => {
      switch (event.key) {
        case 'ArrowRight':
          if (state.canNavigateNext) {
            dispatch({ type: NAVIGATE_NEXT })
          }
          break
        case 'ArrowLeft':
          if (state.canNavigatePrevious) {
            dispatch({ type: NAVIGATE_PREVIOUS })
          }
          break
        default:
          break
      }
    }

    if (open) {
      window.addEventListener('keydown', handleKeyDown)
    }

    return () => {
      window.removeEventListener('keydown', handleKeyDown)
    }
  }, [open, state.canNavigateNext, state.canNavigatePrevious])

  if (medias === null || medias.length === 0) {
    handleClose?.()
    return null
  }

  const countIndicatorText = `${state.selectedMediaIndex + 1}/${medias.length}`
  const galleryWidth = window.innerWidth - 176
  const galleryHeight = window.innerHeight - 140

  function scaleImage(imageHeight, imageWidth, screenHeight, screenWidth) {
    // Calculate the aspect ratio of the image
    const aspectRatio = imageWidth / imageHeight

    // Initialize scaled dimensions
    let scaledWidth, scaledHeight

    // Check if the limiting factor is width or height
    if (screenWidth / screenHeight < aspectRatio) {
      // Screen is narrower in comparison to the image
      scaledWidth = Math.min(screenWidth, imageWidth)
      scaledHeight = scaledWidth / aspectRatio
    } else {
      // Screen is taller in comparison to the image
      scaledHeight = Math.min(screenHeight, imageHeight)
      scaledWidth = scaledHeight * aspectRatio
    }

    // Ensure the image is not larger than the screen
    scaledWidth = Math.min(scaledWidth, screenWidth)
    scaledHeight = Math.min(scaledHeight, screenHeight)

    return { height: scaledHeight, width: scaledWidth }
  }
  let scaledHeight: number, scaledWidth: number

  scaledHeight = galleryHeight
  scaledWidth = galleryWidth

  if (state.currentMedia.mimetype === 'gif') {
    // GiphyGif is a wrapper around Giphy's Gif component
    // and do not keep the aspect ratio so we need to
    // calculate the scaled height and width
    const scaled = scaleImage(state.currentMedia?.height, state.currentMedia?.width, galleryHeight, galleryWidth)
    scaledHeight = scaled.height
    scaledWidth = scaled.width
  }

  return (
    <Dialog
      PaperProps={{
        style: {
          backgroundColor: '#000000',
        },
      }}
      open={open}
      fullScreen
      onClose={handleClose}
    >
      <Stack direction="row" className={classes.topNav} gap={1}>
        <Chip
          sx={{
            backgroundColor: '#35373c',
            borderRadius: '30px',
            padding: '20px 9px 19px 15px',
            fontFamily: 'Graphik Semibold',
            fontWeight: 500,
            fontSize: '13px',
            color: '#e5e5e6',
            '& .MuiChip-label': {
              padding: '0px 8px',
              minWidth: '40px',
            },
          }}
          icon={<Photos20Icon />}
          label={countIndicatorText}
        />
        <IconButton
          sx={{
            color: '#8e94a2',
            backgroundColor: '#17171a',
            transition: 'background-color 0.3s ease-in-out',
            '&:hover': {
              backgroundColor: '#2c2c2e',
            },
          }}
          onClick={handleClose}
        >
          <CloseIcon />
        </IconButton>
      </Stack>
      <Box
        sx={{
          position: 'absolute',
          left: isMobile ? '10px' : '32px',
          top: '50%',
          transform: 'translateY(-50%)',
          zIndex: 1,
        }}
      >
        <IconButton
          sx={{
            color: '#8e94a2',
            backgroundColor: '#17171a',
            transition: 'background-color 0.3s ease-in-out',
            '&:hover': {
              backgroundColor: '#2c2c2e',
            },
          }}
          onClick={() => dispatch({ type: NAVIGATE_PREVIOUS })}
          disabled={!state.canNavigatePrevious}
        >
          <ChevronLeft24Icon />
        </IconButton>
      </Box>
      <div className="flex h-full w-full items-center justify-center p-16">
        {state.currentMedia.mimetype === 'gif' && (
          <GiphyGif
            id={state.currentMedia.id}
            className="max-h-full max-w-full object-scale-down"
            width={scaledWidth}
          />
        )}
        {state.currentMedia.mimetype === 'video' && (
          <div className="flex items-center">
            <AppVideo url={state.currentMedia.url} canPlay={true} height={scaledHeight} />
          </div>
        )}
        {state.currentMedia.mimetype === 'image' && (
          <Image
            width={scaledWidth}
            height={scaledHeight}
            src={state.currentMedia.url}
            alt="Gallery"
            className={classes.media}
          />
        )}
        <Box
          style={{
            position: 'absolute',
            right: isMobile ? '10px' : '32px',
            top: '50%',
            transform: 'translateY(-50%)',
            zIndex: 1,
          }}
        >
          <IconButton
            sx={{
              color: '#8e94a2',
              backgroundColor: '#17171a',
              transition: 'background-color 0.3s ease-in-out',
              '&:hover': {
                backgroundColor: '#2c2c2e',
              },
            }}
            onClick={() => dispatch({ type: NAVIGATE_NEXT })}
            disabled={!state.canNavigateNext}
          >
            <SVGChevronRight />
          </IconButton>
        </Box>
      </div>
    </Dialog>
  )
}

export default FullScreenMediaViewer
