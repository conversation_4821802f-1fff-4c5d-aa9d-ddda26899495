import Button from '@mui/material/Button'
import { makeStyles } from '@mui/styles'
import React, { useState } from 'react'

import SVGChevronDown from '../../svgs/chevron-down'
import useAppTheme from '@/memberup/components/hooks/use-app-theme'
import SVGHashtag from '@/memberup/components/svgs/hashtag'
import SpaceMenu from '@/src/components/dialogs/feed/space-menu'

export interface SpaceMenuProps {
  open: boolean
  selectedValue: string
  onClose: (value?: string) => void
  anchorEl: Element
  spaces: any[]
}

const useStyles = makeStyles((theme) => ({
  menu: {
    zIndex: 1400, // NOTE: Must be greater than 1300 which is the modal zIndex.
  },
  space: {
    opacity: 1,
    color: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.87)' : '#000000',
    backgroundColor: theme.palette.mode === 'dark' ? '#202125' : '#f2f2f3',
    padding: '8px 10px',
    borderRadius: '10px',
    fontFamily: 'Graphik Semibold',
    fontSize: 13,
    fontWeight: 500,
    fontStyle: 'normal',
    letterSpacing: 0,
    textAlign: 'left',
  },
  spaceSelector: {
    borderRadius: 12,
  },
}))

export default function SpaceSelector({ hasErrors, selectedSpace, spaces, disabled = false, onSelectionChange }) {
  const classes = useStyles()
  const [open, setOpen] = React.useState(false)
  const [anchorEl, setAnchorEl] = useState(null)
  const { theme } = useAppTheme()

  const handleClickOpen = (e) => {
    setOpen(true)
    setAnchorEl(e.currentTarget)
  }

  const handleClose = (value: any) => {
    setOpen(false)
    if (onSelectionChange) {
      onSelectionChange(value)
    }
  }

  return disabled ? (
    <div className="flex items-center space-x-4 space-y-6 rounded-[10px] bg-black-300 px-2">
      <SVGHashtag />
      &nbsp;{selectedSpace?.name}
    </div>
  ) : (
    <>
      <Button
        className={classes.spaceSelector}
        sx={{
          color: hasErrors ? '#ffffff' : theme.palette.mode === 'dark' ? '#ffffff' : '#000000',
          backgroundColor: hasErrors ? '#7e221a' : theme.palette.mode === 'dark' ? 'rgba(141,148,163,0.08)' : '#f2f2f3',
          padding: '8px 10px',
          '&:hover': {
            backgroundColor: hasErrors
              ? '#7e221a'
              : theme.palette.mode === 'dark'
                ? '#202125 !important'
                : '#f2f2f3 !important',
          },
        }}
        onClick={handleClickOpen}
      >
        <SVGHashtag
          styles={{
            color: hasErrors ? '#fff' : theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.87)' : '#000000',
          }}
        />
        <span
          className={'whitespace-nowrap'}
          style={{
            color: hasErrors ? '#fff' : theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.87)' : '#000000',
          }}
        >
          &nbsp;{selectedSpace?.name || 'Choose space'}{' '}
          {!disabled && (
            <SVGChevronDown
              styles={{
                color: hasErrors ? 'fff' : theme.palette.mode === 'dark' ? '#8d94a2' : '#8d94a2',
                marginLeft: '5px',
              }}
            />
          )}
        </span>
      </Button>
      <SpaceMenu anchorEl={anchorEl} spaces={spaces} selectedValue={selectedSpace} open={open} onClose={handleClose} />
    </>
  )
}
