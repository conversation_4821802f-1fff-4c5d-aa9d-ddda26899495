import {
  Brightness1,
  FacebookOutlined,
  Instagram,
  LocationOnOutlined,
  PublicOutlined,
  YouTube,
} from '@mui/icons-material'
import EditOutlinedIcon from '@mui/icons-material/EditOutlined'
import { Box, Chip, Grid } from '@mui/material'
import Button from '@mui/material/Button'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import IconButton from '@mui/material/IconButton'
import Slide, { SlideProps } from '@mui/material/Slide'
import useTheme from '@mui/material/styles/useTheme'
import Typography from '@mui/material/Typography'
import useMediaQuery from '@mui/material/useMediaQuery'
import { makeStyles } from '@mui/styles'
import clsx from 'clsx'
import { useRouter } from 'next/router'
import React, { useEffect, useMemo, useRef, useState } from 'react'
import InfiniteScroll from 'react-infinite-scroll-component'

import PostLoader from '../../common/loaders/post-loader'
import useCheckUserRole from '../../hooks/use-check-user-role'
import SVGCloseNew from '../../svgs/close-new'
import SVGTikTok from '../../svgs/tiktok-logo'
import SVGXLogo from '../../svgs/x-logo'
import { AppProfileImage } from '@memberup/shared/src/components/common/profile-image'
import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { adjustRGBA } from '@memberup/shared/src/libs/color'
import { formatLastActiveDate } from '@memberup/shared/src/libs/date-utils'
import { getFullName } from '@memberup/shared/src/libs/profile'
import { ACTIVITY_ENUM, USER_ROLE_ENUM } from '@memberup/shared/src/types/enum'
import { IUser, IUserProfile } from '@memberup/shared/src/types/interfaces'
import NoActivity from '@/memberup/components/feed/no-activity'
import useAppTheme from '@/memberup/components/hooks/use-app-theme'
import SVGMessage from '@/memberup/components/svgs/message'
import SVGVerified from '@/memberup/components/svgs/verified'
import { getStreamChatClient } from '@/memberup/libs/getstream'
import { selectRequestGetMember } from '@/memberup/store/features/memberSlice'
import { selectRequestGetActiveUser } from '@/memberup/store/features/userSlice'
import { useAppSelector } from '@/memberup/store/hooks'

const maxFeedAttachmentHeight = 400

const useStyles = makeStyles((theme) => ({
  root: {
    width: '552px',
    maxWidth: '100%',
    minWidth: 380,
    marginLeft: 'auto',
    '& .MuiDialog-container': {
      alignItems: 'unset',
    },
    '& .MuiDialog-paper': {
      backgroundColor: theme.palette.background.paper,
    },
    '& .MuiDialog-paperFullWidth': {
      width: '100%',
      height: '100%',
      maxHeight: '100%',
      maxWidth: '100%',
      margin: 0,
    },
    '& .MuiDialogTitle-root': {
      borderBottom: 'none',
      paddingTop: 38,
    },
    '& .MuiDialogContent-root': {
      minHeight: 320,
      lineHeight: 1,
      padding: 16,
      paddingTop: 16,
      '& img': {
        borderRadius: 16,
      },
    },
    '&.mobile': {
      width: '100%',
      '& .MuiDialog-container': {
        alignItems: 'flex-end',
      },
      '& .MuiDialog-paper': {
        borderTopLeftRadius: 24,
        borderTopRightRadius: 24,
        width: '100%',
      },
      '& .MuiDialog-paperFullWidth': {
        height: '60%',
        maxHeight: '60%',
      },
      '& .MuiDialogTitle-root': {
        paddingTop: 0,
      },
      '& .MuiDialogContent-root': {
        padding: 0,
      },
    },
  },
  mediaIcon: {
    fontSize: '24px',
    marginRight: '8px',
    cursor: 'pointer',
  },
  chips: {
    fontFamily: 'Graphik Medium',
    fontSize: '14px',
    borderWidth: '1px',
    borderStyle: 'solid',
    borderColor: theme.palette.mode == 'dark' ? '#FFF' : '#000',
    marginTop: '8px',
    marginRight: '8px',
    padding: '4px 4px',
  },
  content: {
    maxWidth: 682,
    margin: 'auto',
    padding: 16,
  },
  photoWrapper: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    margin: 'auto',
    width: 200,
    height: 200,
    overflow: 'hidden',
    backgroundColor: '#EBE2DD',
    borderRadius: '42%',
  },
  personIcon: {
    fontSize: 96,
    opacity: 0.2,
  },
  activityCardWrapper: {
    width: '100%',
    marginBottom: 16,
    lineHeight: 1,
  },
  noPost: {
    borderRadius: 8,
    padding: 12,
  },
}))

// eslint-disable-next-line react/display-name
const Transition = React.forwardRef<unknown, SlideProps>((props, ref) => (
  <Slide direction="left" {...props} ref={ref} />
))

// eslint-disable-next-line react/display-name
const TransitionUp = React.forwardRef<unknown, SlideProps>((props, ref) => (
  <Slide direction="up" {...props} ref={ref} />
))

const UserProfile: React.FC<{
  user: IUser
  userProfile?: IUserProfile
  onlineStatus: boolean
  isMe: boolean
  open: boolean
  onEditProfile: () => void
  onClose: () => void
}> = ({ user, userProfile, isMe, onlineStatus, open, onEditProfile, onClose }) => {
  const profile = userProfile || user?.profile
  const classes = useStyles()
  const dialogContentEleRef = useRef(null)
  const theme = useTheme()
  const isSmDown = useMediaQuery(theme.breakpoints.down('sm'))
  const router = useRouter()
  const client = getStreamChatClient()
  const { isCurrentUserAdmin } = useCheckUserRole()
  const { isDarkTheme } = useAppTheme()
  const requestGetActiveUser = useAppSelector((state) => selectRequestGetActiveUser(state))
  const requestGetMember = useAppSelector((state) => selectRequestGetMember(state))
  const mountedRef = useMounted(true)
  const [chatUserInfo, setChatUserInfo] = useState<any>(null)
  const [activities] = useState({
    activities: [],
    hasMore: false,
    next: null,
  })
  const isAdminOrOwner = user?.role && [USER_ROLE_ENUM.admin, USER_ROLE_ENUM.owner].includes(user.role as any)

  const handleClickMessage = () => {
    onClose()
    router.push({ pathname: `/inbox`, query: { member: user.id } }, undefined, { shallow: false })
  }

  useEffect(() => {
    if (!mountedRef.current || !user) return
    const fetchData = async () => {
      const response = await client.queryUsers({
        id: user.id,
      })
      console.log(response.users[0], 'response.users[0]')
      setChatUserInfo(response.users[0])
    }
    fetchData()
  }, [user])

  const renderPosts = useMemo(() => {
    if (profile?.hide_my_activity) return null

    const posts = activities.activities.filter((item) => item.verb === ACTIVITY_ENUM.post)
    return (
      <InfiniteScroll
        dataLength={posts.length}
        next={() => {
          // fetchMoreActivities(null, activities.activities[activities.activities.length - 1]?.id)
        }}
        hasMore={activities.hasMore}
        loader={
          <div>
            <PostLoader />
          </div>
        }
        scrollableTarget="profile-dialog-content"
      >
        {posts.map((item, index) => (
          <div className={classes.activityCardWrapper} key={`post-${index}`}>
            {/* <FeedCard feed={item} maxAttachmentHeight={maxFeedAttachmentHeight} /> */}
          </div>
        ))}
        {/* {(requestGetActiveUser || requestGetMember) && renderPostLoader} */}
        {!requestGetActiveUser && !requestGetMember && !activities.hasMore && !posts.length && (
          <NoActivity text="No Activity" />
        )}
      </InfiniteScroll>
    )
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [requestGetActiveUser, requestGetMember, profile?.hide_my_activity, activities, maxFeedAttachmentHeight])
  // open url in a new tab
  const goTo = (url) => {
    url = url.match(/^https?:/) ? url : '//' + url
    window.open(url, '_blank')
  }

  const renderMButton = useMemo(() => {
    const startIcon = isMe ? <EditOutlinedIcon color="inherit" /> : <SVGMessage width={18} height={18} />
    const onClick = isMe ? onEditProfile : handleClickMessage
    const label = isMe ? 'Edit Profile' : 'Message'
    const dataCy = isMe ? 'edit-profile-button' : 'direct-message-button'
    return (
      <Button
        className="app-button round-small"
        variant="contained"
        color="primary"
        fullWidth={isSmDown}
        startIcon={startIcon}
        onClick={onClick}
        data-cy={dataCy}
        sx={{
          color: 'white',
          backgroundColor: adjustRGBA(theme.palette.primary.main, 0.8),
        }}
      >
        {label}
      </Button>
    )
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isMe, isSmDown])

  const renderLastActive = useMemo(() => {
    const lastActive = chatUserInfo?.last_active
    if (!lastActive) return null
    const formattedLastActive = formatLastActiveDate(lastActive)
    return (
      <Typography
        sx={{
          fontFamily: 'Graphik Medium',
          fontSize: '12px',
          lineHeight: '16px',
        }}
      >
        <span style={{ color: 'rgb(141, 148, 163)' }}>Last Active</span> {formattedLastActive}
      </Typography>
    )
  }, [chatUserInfo])

  const NameElement = () => (
    <Typography
      variant="h4"
      color="inherit"
      sx={{
        fontFamily: 'Graphik SemiBold',
        fontWeight: 700,
        fontSize: isSmDown ? '20px' : '24px',
        lineHeight: isSmDown ? '26px' : '32px',
        letterSpacing: '-0.5px',
      }}
    >
      {getFullName(user?.first_name, user?.last_name)}
      {(isAdminOrOwner || (isMe && isCurrentUserAdmin)) && <SVGVerified styles={{ marginLeft: 5 }} />}
    </Typography>
  )

  return (
    <Dialog
      maxWidth="lg"
      fullWidth={true}
      className={clsx(classes.root, { mobile: isSmDown })}
      open={open}
      onClose={onClose}
      TransitionComponent={isSmDown ? TransitionUp : Transition}
      TransitionProps={{
        in: open,
        timeout: {
          appear: 500,
          enter: 500,
          exit: 300,
        },
      }}
      aria-labelledby="profile-dialog-title"
    >
      <DialogTitle id="profile-dialog-title">
        <IconButton
          sx={{
            background: theme.palette.mode === 'dark' ? '#17171a' : '#ffffff',
            color: '#8D94A3',
            p: '14px',
            m: '6px',
            width: '40px',
            height: '40px',
          }}
          size="medium"
          aria-label="close"
          className="close large"
          onClick={onClose}
        >
          <SVGCloseNew />
        </IconButton>
      </DialogTitle>
      <DialogContent id="profile-dialog-content" ref={dialogContentEleRef}>
        <Box className={classes.content}>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <Grid container>
                <Grid item sx={{ position: 'relative' }}>
                  <AppProfileImage
                    imageUrl={profile?.image || user?.image}
                    cropArea={profile?.image_crop_area || user?.image_crop_area}
                    name={user?.first_name}
                    r1={0.14}
                    r2={0.5}
                    size={isSmDown ? 120 : 200}
                    fontSize={64}
                  />
                  {onlineStatus && (
                    <div
                      style={{
                        position: 'absolute',
                        right: 15,
                        bottom: 8,
                        backgroundColor: isDarkTheme ? '#AEE78B' : '#48B705',
                        width: 20,
                        height: 20,
                        borderRadius: '50%',
                        borderStyle: 'solid',
                        borderColor: theme.palette.background.paper,
                        borderWidth: 2,
                      }}
                    ></div>
                  )}
                </Grid>
                {isSmDown ? (
                  <Grid
                    item
                    xs
                    container
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      marginLeft: '16px',
                      marginTop: '20px',
                      rowGap: '6px',
                    }}
                  >
                    <Grid item xs={12}>
                      <NameElement />
                    </Grid>
                    <Grid item xs={12}>
                      {renderLastActive}
                    </Grid>
                    <Grid item xs={12}>
                      {renderMButton}
                    </Grid>
                  </Grid>
                ) : (
                  <Grid item xs sx={{ display: 'flex', alignItems: 'center', justifyContent: 'end' }}>
                    <Box>
                      <Grid item xs className="text-right">
                        {renderMButton}
                      </Grid>
                      <Grid item xs style={{ marginTop: '16px' }}>
                        {renderLastActive}
                      </Grid>
                    </Box>
                  </Grid>
                )}
              </Grid>
            </Grid>
            <Grid item xs={12}>
              {!isSmDown && <NameElement />}
              <Box style={{ marginTop: 12 }}>
                {Boolean(profile?.bio) ? (
                  <Typography variant="body1" color="inherit" sx={{ whiteSpace: 'pre-wrap' }}>
                    {profile?.bio}
                  </Typography>
                ) : (
                  <Typography color="text.disabled" variant="body1">
                    This member has not shared their bio yet.
                  </Typography>
                )}
              </Box>
            </Grid>
            <Grid item xs={12} style={{ marginTop: 10 }}>
              {userProfile?.personality_type && userProfile?.personality_type !== 'DS' && (
                <Chip
                  icon={<Brightness1 fontSize={'inherit'} />}
                  label={userProfile?.personality_type}
                  variant="outlined"
                  className={classes.chips}
                />
              )}
              {userProfile?.location && (
                <Chip
                  icon={<LocationOnOutlined />}
                  label={userProfile?.location}
                  variant="outlined"
                  className={classes.chips}
                />
              )}
            </Grid>
            <Grid item xs={12}>
              {profile?.social?.website && (
                <IconButton onClick={() => goTo(profile?.social?.website)} className={classes.mediaIcon}>
                  <PublicOutlined />
                </IconButton>
              )}
              {profile?.social?.facebook && (
                <IconButton
                  onClick={() => goTo('https://facebook.com/' + profile?.social?.facebook)}
                  className={classes.mediaIcon}
                >
                  <FacebookOutlined />
                </IconButton>
              )}
              {profile?.social?.instagram && (
                <IconButton
                  onClick={() => goTo('https://instagram.com/' + profile?.social?.instagram)}
                  className={classes.mediaIcon}
                >
                  <Instagram />
                </IconButton>
              )}
              {profile?.social?.youtube && (
                <IconButton
                  onClick={() => goTo('https://youtube.com/@' + profile?.social?.youtube)}
                  className={classes.mediaIcon}
                >
                  <YouTube />
                </IconButton>
              )}
              {profile?.social?.x && (
                <IconButton
                  sx={{ padding: '10px' }}
                  onClick={() => goTo('https://x.com/' + profile?.social?.x)}
                  className={classes.mediaIcon}
                >
                  <SVGXLogo width={'16px'} height={'16px'} />
                </IconButton>
              )}
              {profile?.social?.tiktok && (
                <IconButton
                  sx={{ padding: '10px' }}
                  onClick={() => goTo('https://tiktok.com/@' + profile?.social?.tiktok)}
                  className={classes.mediaIcon}
                >
                  <SVGTikTok width={'16px'} height={'16px'} />
                </IconButton>
              )}
            </Grid>
          </Grid>
        </Box>
      </DialogContent>
    </Dialog>
  )
}
export default UserProfile
