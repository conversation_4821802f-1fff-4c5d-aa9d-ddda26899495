import Button from '@mui/material/Button'
import Checkbox from '@mui/material/Checkbox'
import CircularProgress from '@mui/material/CircularProgress'
import FormControlLabel from '@mui/material/FormControlLabel'
import Grid from '@mui/material/Grid'
import useTheme from '@mui/material/styles/useTheme'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import { useEffect } from 'react'
import { Controller, useForm } from 'react-hook-form'

import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { adjustRGBA } from '@memberup/shared/src/libs/color'
import { NOTIFICATION_SETTINGS } from '@memberup/shared/src/settings/notifications'
import { selectRequestUpdateProfile, selectUserProfile, updateUserProfile } from '@/memberup/store/features/userSlice'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'

const useStyles = makeStyles((theme) => ({
  root: {
    color: theme.palette.text.disabled,
    height: '100%',
    overflow: 'hidden',
    '& .MuiFormControlLabel-root': {
      marginRight: 0,
    },
  },
}))

const FormValue: any = {}

NOTIFICATION_SETTINGS.forEach((item) => {
  FormValue[`${item.name}.email`] = item.email
  FormValue[`${item.name}.in_app_feed`] = item.in_app_feed
})

export default function EditNotifications({ handleCancel }: { handleCancel?: () => void }) {
  const classes = useStyles()
  const dispatch = useAppDispatch()
  const theme = useTheme()
  const mountedRef = useMounted(true)
  const userProfile = useAppSelector((state) => selectUserProfile(state))
  const requestUpdateProfile = useAppSelector((state) => selectRequestUpdateProfile(state))
  const { control, reset, formState, handleSubmit } = useForm({
    mode: 'onChange',
    reValidateMode: 'onChange',
    defaultValues: FormValue,
  })

  useEffect(() => {
    if (!mountedRef.current) return
    const temp = {}
    for (const item of NOTIFICATION_SETTINGS) {
      temp[item.name] = {
        email: userProfile?.enable_notifications?.[item.name]?.['email'],
        in_app_feed: userProfile?.enable_notifications?.[item.name]?.['in_app_feed'],
      }
    }
    reset(temp)
    return () => {}
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userProfile])

  const handleContinue = (payload) => {
    dispatch(
      updateUserProfile({
        data: {
          enable_notifications: payload,
        },
        successMessage: 'The account has been updated successfully.',
      }),
    )
  }

  return (
    <div className={classes.root}>
      <form autoComplete="off" onSubmit={handleSubmit(handleContinue)}>
        <div className="formContent">
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <Grid container alignItems="center" spacing={2}>
                <Grid item xs>
                  <Typography variant="h6">New Activity</Typography>
                </Grid>
                <Grid item sx={{ width: 64 }}>
                  <Typography variant="body1">Email</Typography>
                </Grid>
                <Grid item sx={{ width: 64 }}>
                  <Typography variant="body1">In-app</Typography>
                </Grid>
              </Grid>
            </Grid>
            {NOTIFICATION_SETTINGS.map((item) => (
              <Grid item key={item.name} xs={12}>
                <Grid container alignItems="center" spacing={2}>
                  <Grid item xs>
                    <Typography variant="body1">{item.title}</Typography>
                  </Grid>
                  <Grid item className="text-center" sx={{ width: 64 }}>
                    <Controller
                      render={({ field: { value, onChange, onBlur }, field, fieldState: { error } }) => {
                        return (
                          <FormControlLabel
                            control={
                              <Checkbox
                                checked={value}
                                onChange={(e) => {
                                  onChange(e.target.checked)
                                }}
                                name={`${item.name}.email`}
                                disabled={item.email_disabled}
                              />
                            }
                            label=""
                          />
                        )
                      }}
                      control={control}
                      name={`${item.name}.email`}
                    />
                  </Grid>
                  <Grid item className="text-center" sx={{ width: 64 }}>
                    <Controller
                      render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={value}
                              onChange={(e) => {
                                onChange(e.target.checked)
                              }}
                              name={`${item.name}.in_app_feed`}
                            />
                          }
                          label=""
                        />
                      )}
                      control={control}
                      name={`${item.name}.in_app_feed`}
                    />
                  </Grid>
                </Grid>
              </Grid>
            ))}
          </Grid>
        </div>

        <div className="buttonsWrapper text-center">
          <Button
            className="app-button round-small"
            color="primary"
            disabled={requestUpdateProfile}
            variant="outlined"
            onClick={() => handleCancel?.()}
          >
            Cancel
          </Button>
          <Button
            className="app-button round-small"
            sx={{ backgroundColor: adjustRGBA(theme.palette.primary.main, 0.8) }}
            color="primary"
            disabled={requestUpdateProfile}
            type="submit"
            variant="contained"
          >
            {requestUpdateProfile ? <CircularProgress size={16} color="inherit" /> : 'Save'}
          </Button>
        </div>
      </form>
    </div>
  )
}
