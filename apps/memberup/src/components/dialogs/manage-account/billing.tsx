import ChevronRightIcon from '@mui/icons-material/ChevronRight'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import Chip from '@mui/material/Chip'
import Divider from '@mui/material/Divider'
import Grid from '@mui/material/Grid'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import { saveAs } from 'file-saver'
import { useEffect, useMemo, useState } from 'react'

import AddPaymentMethod from './add-payment-method'
import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { formatDate } from '@memberup/shared/src/libs/date-utils'
import { numberToCurrency } from '@memberup/shared/src/libs/numeric-utils'
import { getStripeCurrentPaymentMethodApi, getStripeInvoicesApi } from '@memberup/shared/src/services/apis/stripe.api'
import { USER_ROLE_ENUM } from '@memberup/shared/src/types/enum'
import LoadingSpinner from '@/memberup/components/common/loaders/loading-spinner'
import { selectUser, selectUserProfile } from '@/memberup/store/features/userSlice'
import { useAppSelector } from '@/memberup/store/hooks'

const useStyles = makeStyles((theme) => ({
  root: {
    color: theme.palette.text.disabled,
    overflow: 'hidden',
  },
  section: {
    borderColor: theme.palette.divider,
    borderRadius: 8,
    borderStyle: 'solid',
    borderWidth: 1,
    padding: 16,
  },
}))

export default function Billing() {
  const classes = useStyles()
  const mountedRef = useMounted(true)
  const user = useAppSelector(selectUser)
  const userProfile = useAppSelector(selectUserProfile)
  const [openAddPaymentMethod, setOpenAddPaymentMethod] = useState(false)
  const [requestGetInvoices, setRequestGetInvoices] = useState(false)
  const [paymentMethod, setPaymentMethod] = useState(null)
  const [invoices, setBalanceInvoices] = useState({
    has_more: true,
    data: [],
  })

  const fetchInvoices = async () => {
    if (requestGetInvoices) return
    setRequestGetInvoices(true)
    getStripeInvoicesApi(true)
      .then((result) => {
        if (mountedRef.current) {
          setBalanceInvoices({
            has_more: result.data.data.has_more,
            data: result.data.data.data,
          })
        }
      })
      .finally(() => {
        if (mountedRef.current) {
          setRequestGetInvoices(false)
        }
      })
  }

  useEffect(() => {
    fetchInvoices()
    getStripeCurrentPaymentMethodApi(true)
      .then((result) => {
        if (mountedRef.current) {
          setPaymentMethod(result.data.data)
        }
      })
      .catch((err) => {
        console.log(err)
      })
    return () => {}
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const nextBilingDate = useMemo(() => {
    if (!userProfile.stripe_subscription_period_end_at && !userProfile.stripe_subscription?.current_period_end)
      return null
    return formatDate({
      date: new Date(
        (userProfile.stripe_subscription_period_end_at || userProfile.stripe_subscription?.current_period_end) * 1000,
      ),
      format: 'LLLL dd, yyyy',
    })
  }, [userProfile])

  const handleDownloadInvoice = (receiptUrl: string) => {
    const urlObj = new URL(receiptUrl)
    urlObj.pathname = urlObj.pathname + '/pdf'
    saveAs(urlObj.href)
  }

  return (
    <div className={classes.root}>
      {user?.role === USER_ROLE_ENUM.admin && (
        <p>Note: This section is disabled as you are logged in as a Creator Role</p>
      )}
      <Stack direction="row" sx={{ mb: 2 }} spacing={1}>
        {userProfile.stripe_subscription_status === 'active' ? (
          <Chip label="Active" color="success" />
        ) : (
          <Chip label="Inactive" color="default" />
        )}

        {userProfile.stripe_subscription_cancel_at_period_end && (
          <Chip label={`Cancels ${nextBilingDate}`} color="default" />
        )}
      </Stack>
      <Box className={classes.section} sx={{ mb: 4 }}>
        <Stack spacing={2}>
          <Typography variant="subtitle1">Payment Info</Typography>
          <Typography variant="body2" color="inherit" textTransform="capitalize">
            {paymentMethod?.card?.brand} ... ... ... {paymentMethod?.card?.last4}
          </Typography>
          {userProfile.stripe_subscription_cancel_at_period_end ? (
            <Typography variant="body2" color="inherit">
              Your subscription will be expired at {nextBilingDate || 'unknown'}.
            </Typography>
          ) : (
            <Typography variant="body2" color="inherit">
              Your next billing date is {nextBilingDate || 'unknown'}.
            </Typography>
          )}

          <Divider />
          <Button
            className="no-padding"
            variant="text"
            color="primary"
            endIcon={<ChevronRightIcon />}
            onClick={() => setOpenAddPaymentMethod(true)}
          >
            Update Payment Method
          </Button>
        </Stack>
      </Box>
      <Box className={classes.section}>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom fontWeight={700}>
              Billing History
            </Typography>
          </Grid>
          <Grid item xs={12} sx={{ position: 'relative' }}>
            {requestGetInvoices ? (
              <LoadingSpinner size={24} />
            ) : (
              <>
                {!invoices.data?.length ? (
                  <Typography color="text.disabled" variant="body1">
                    No invoices
                  </Typography>
                ) : (
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <Grid container alignItems="center" spacing={3}>
                        <Grid item sm={4} xs={5}>
                          <Typography color="text.disabled" variant="body1">
                            Amount
                          </Typography>
                        </Grid>
                        <Grid item sm={3} xs={3}>
                          <Typography color="text.disabled" variant="body1">
                            Date
                          </Typography>
                        </Grid>
                        <Grid item sm={3} xs={4}>
                          <Typography color="text.disabled" variant="body1">
                            Receipt
                          </Typography>
                        </Grid>
                      </Grid>
                    </Grid>
                    {invoices.data.map((item) => (
                      <Grid key={item.id} item xs={12}>
                        <Grid container alignItems="center" spacing={3}>
                          <Grid item sm={2} xs={3}>
                            <Typography variant="body1">{numberToCurrency(item.amount_due / 100)}</Typography>
                          </Grid>
                          <Grid item xs={2}>
                            <Typography color={item.paid ? 'primary' : 'text.disabled'} variant="body1">
                              {item.paid ? 'Paid' : 'Unpaid'}
                            </Typography>
                          </Grid>
                          <Grid item sm={3} xs={4}>
                            <Typography variant="body1">
                              {item.status_transitions.paid_at &&
                                formatDate({
                                  date: new Date(item.status_transitions.paid_at * 1000),
                                  format: 'LLLL dd, yyyy',
                                })}
                            </Typography>
                          </Grid>
                          {item.charge?.receipt_url && (
                            <Grid item sm={3} xs={4}>
                              <Button
                                className="no-padding"
                                variant="text"
                                onClick={() => handleDownloadInvoice(item.charge.receipt_url)}
                              >
                                Download
                              </Button>
                            </Grid>
                          )}
                        </Grid>
                      </Grid>
                    ))}
                  </Grid>
                )}
              </>
            )}
          </Grid>
        </Grid>
      </Box>
      {openAddPaymentMethod && (
        <AddPaymentMethod
          open={openAddPaymentMethod}
          isMembership={true}
          onClose={(e) => {
            if (e) {
              setPaymentMethod(e)
            }
            setOpenAddPaymentMethod(false)
          }}
        />
      )}
    </div>
  )
}
