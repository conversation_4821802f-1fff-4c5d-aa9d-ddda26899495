import { joiResolver } from '@hookform/resolvers/joi'
import CloseIcon from '@mui/icons-material/Close'
import Button from '@mui/material/Button'
import CircularProgress from '@mui/material/CircularProgress'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import FormControl from '@mui/material/FormControl'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import TextField from '@mui/material/TextField'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import clsx from 'clsx'
import Joi from 'joi'
import React, { useMemo, useState } from 'react'
import { Controller, useForm } from 'react-hook-form'
import { toast } from 'react-toastify'

import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { formatDate } from '@memberup/shared/src/libs/date-utils'
import { showToast } from '@memberup/shared/src/libs/toast'
import { cancelMembershipApi } from '@memberup/shared/src/services/apis/user.api'
import { selectUserProfile } from '@/memberup/store/features/userSlice'
import { useAppSelector } from '@/memberup/store/hooks'
import { sendCancelMembershipEmailApi } from '@/shared-services/apis/user.api'

const useStyles = makeStyles((theme) => ({
  root: {
    '& .MuiDialog-paper': {
      borderRadius: 12,
    },
  },
  dialogTitle: {
    borderBottom: 'none',
    fontSize: 16,
    color: theme.palette.error.main,
    padding: 24,
  },
  dialogContent: {
    paddingLeft: 24,
    paddingRight: 24,
    paddingTop: 24,
    paddingBottom: 24,
  },
  ul: {
    borderRadius: 12,
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#F3BF46',
    marginTop: 0,
    padding: 16,
    paddingLeft: 32,
  },
}))

type FormDataType = {
  cancelConfirm: string
}

const FormValue: FormDataType = {
  cancelConfirm: '',
}

const FormSchema = Joi.object({
  cancelConfirm: Joi.string()
    .required()
    .regex(/^CANCEL$/)
    .messages({
      'string.empty': `Please enter CANCEL to confirm.`,
      'string.pattern.base': `Please enter CANCEL to confirm.`,
      'any.required': `Please enter CANCEL to confirm.`,
    }),
}).options({ allowUnknown: true })

const CancelMembership: React.FC<{ open: boolean; onClose: (cancelled?: boolean) => void }> = ({ open, onClose }) => {
  const classes = useStyles()
  const mountedRef = useMounted(true)
  const [requestCacnel, setRequestCacnel] = useState(false)
  const userProfile = useAppSelector(selectUserProfile)

  const nextBilingDate = useMemo(() => {
    if (!userProfile.stripe_subscription_period_end_at && !userProfile.stripe_subscription?.current_period_end)
      return null
    return formatDate({
      date: new Date(
        (userProfile.stripe_subscription_period_end_at || userProfile.stripe_subscription?.current_period_end) * 1000,
      ),
      format: 'LLLL dd, yyyy',
    })
  }, [userProfile])

  const hookFormMethods = useForm<FormDataType>({
    reValidateMode: 'onBlur',
    defaultValues: FormValue,
    resolver: joiResolver(FormSchema),
  })
  const { control, register, reset, formState, getValues, setValue, trigger, handleSubmit } = hookFormMethods

  const handleFormSubmit = async (formData) => {
    try {
      setRequestCacnel(true)
      await sendCancelMembershipEmailApi()
      cancelMembershipApi()
        .then((res) => {
          if (res.data.success) {
            showToast('Cancellation successful', 'success', { autoClose: 3000, closeOnClick: true })
            onClose(true)
          }
        })
        .catch((err) => {
          showToast(err.message, 'error', { autoClose: 3000, closeOnClick: true })
        })
        .finally(() => {
          if (mountedRef.current) {
            setRequestCacnel(false)
          }
        })
    } catch (err: any) {
      showToast(err.message, 'error', { autoClose: 3000, closeOnClick: true })

      if (mountedRef.current) {
        setRequestCacnel(false)
      }
    }
  }

  const handleKeepMembership = () => {
    onClose()
  }

  return (
    <Dialog
      maxWidth="xs"
      fullWidth={true}
      className={classes.root}
      open={open}
      onClose={() => onClose()}
      TransitionProps={{
        in: open,
        timeout: {
          appear: 800,
          enter: 800,
          exit: 500,
        },
      }}
      aria-labelledby="feedback-dialog"
    >
      <DialogTitle className={classes.dialogTitle} id="feedback-dialog">
        Cancel Membership
        <IconButton size="small" aria-label="close" className="close large color03" onClick={(e) => onClose()}>
          <CloseIcon fontSize="inherit" />
        </IconButton>
      </DialogTitle>
      <DialogContent className={classes.dialogContent}>
        <form autoComplete="off" onSubmit={handleSubmit(handleFormSubmit)}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography sx={{ fontWeight: 'bold' }} className="color02" variant="body1" gutterBottom>
                We are sorry to see you go!
              </Typography>
              <Typography sx={{ marginTop: '10px' }} className="color02" variant="body1" gutterBottom>
                Please note: Your account will be closed immediately. If you wish to rejoin, you will need to contact
                support to reactivate your account.
              </Typography>
              {/* <Typography
                sx={{ marginTop: '10px' }}
                className="color02"
                variant="body1"
                gutterBottom
              >
                If you wish to rejoin, you will need to contact support to reactivate your account.
              </Typography> */}
            </Grid>
            {/* <Grid item xs={12}>
              <Typography className="font-family-graphik-medium" variant="body1">
                Here&apos;s what to expect:
              </Typography>
            </Grid> */}
            {/* <Grid item xs={12}>
              <ul className={clsx(classes.ul, 'color02')}>
                <li>
                  <Typography variant="body1" color="inherit">
                    Your subscription is paid until {nextBilingDate}. After {nextBilingDate} you can
                    no longer access the community platform
                  </Typography>
                </li>
                <li>
                  <Typography variant="body1" color="inherit">
                    You can reopen anytime. If you restart your membership within 12 months, your
                    profile and account details will be waiting for you.
                  </Typography>
                </li>
                <li>
                  <Typography variant="body1" color="inherit">
                    If you don’t reopen your account after 12 months, we will securely delete your
                    data.
                  </Typography>
                </li>
              </ul>
            </Grid> */}
            <Grid item xs={12}>
              <Typography className="font-family-graphik-medium" variant="body1" gutterBottom>
                To continue, enter &quot;CANCEL&quot;
              </Typography>
              <Controller
                render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => {
                  return (
                    <FormControl error={Boolean(error)} className={clsx('form-control')} fullWidth>
                      <TextField
                        placeholder="Enter CANCEL to confirm"
                        variant="outlined"
                        size="small"
                        error={Boolean(error)}
                        helperText={error?.message}
                        value={value}
                        onChange={(e) => {
                          onChange(e.target.value)
                        }}
                        onBlur={onBlur}
                      />
                    </FormControl>
                  )
                }}
                control={control}
                name="cancelConfirm"
              />
            </Grid>
            <Grid item xs={12}></Grid>
            <Grid item xs={12}></Grid>
            <Grid item xs={6}>
              <Button
                className="app-button round-small"
                variant="outlined"
                color="inherit"
                fullWidth
                disabled={requestCacnel}
                onClick={handleKeepMembership}
              >
                Keep Membership
              </Button>
            </Grid>
            <Grid item xs={6}>
              <Button
                className="app-button round-small"
                variant="contained"
                color="error"
                fullWidth
                disabled={requestCacnel}
                type="submit"
              >
                {requestCacnel ? <CircularProgress size={16} color="inherit" /> : 'Confirm Cancel'}
              </Button>
            </Grid>
          </Grid>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export default CancelMembership
