import ArrowForwardOutlinedIcon from '@mui/icons-material/ArrowForwardOutlined'
import CloseIcon from '@mui/icons-material/Close'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import { useRouter } from 'next/router'
import React from 'react'

import { AppImg } from '@memberup/shared/src/components/common/media/image'
import { selectMembershipAssetsPath } from '@/memberup/store/features/membershipSlice'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'

const useStyles = makeStyles((theme) => ({
  root: {
    padding: 16,
    textAlign: 'center',
    '& .MuiDialog-paper': {
      borderRadius: 12,
      maxWidth: 648,
    },
  },
  dialogTitle: {
    borderBottom: 'none',
    paddingTop: 40,
    paddingBottom: 0,
  },
  dialogContent: {
    position: 'relative',
    padding: '16px 32px 32px 32px',
    '&.MuiDialogContent-root': {
      paddingTop: 16,
    },
  },
  videoSetupItem: {
    backgroundColor: theme.palette.action.disabledBackground,
    borderColor: 'transparent',
    borderRadius: 8,
    borderStyle: 'solid',
    borderWidth: 2,
    cursor: 'pointer',
    lineHeight: 1,
    padding: '32px 24px',
    textAlign: 'center',
    height: 354,
    '& .MuiIconButton-root': {
      borderColor: theme.palette.text.disabled,
      borderStyle: 'solid',
      borderWidth: 1,
      width: 56,
      height: 56,
    },
    '&:hover': {
      borderColor: theme.palette.primary.main,
      '& .MuiIconButton-root': {
        background: theme.palette.primary.main,
        borderColor: theme.palette.primary.main,
      },
    },
  },
  videoSetupItemText: {
    minHeight: 100,
  },
}))

const LiveVideoSetup: React.FC<{
  open: boolean
  onClose: () => void
}> = ({ open, onClose }) => {
  const classes = useStyles()
  const router = useRouter()
  const dispatch = useAppDispatch()
  const membershipAssetsPath = useAppSelector((state) => selectMembershipAssetsPath(state))

  const handleSelectLiveVideoSetup = (e: string) => {
    // router.push(`/live/cameo`)
    onClose()
  }

  return (
    <Dialog
      maxWidth="md"
      fullWidth={true}
      className={classes.root}
      open={open}
      onClose={onClose}
      TransitionProps={{
        in: open,
        timeout: {
          appear: 800,
          enter: 800,
          exit: 500,
        },
      }}
      aria-labelledby="select-live-video-setup-title"
    >
      <DialogTitle className={classes.dialogTitle} id="select-live-video-setup-title">
        Select Live Video Setup
        <IconButton size="small" aria-label="close" className="close large" onClick={onClose}>
          <CloseIcon fontSize="inherit" />
        </IconButton>
      </DialogTitle>
      <DialogContent className={classes.dialogContent}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <Typography className="color04" variant="body2" color="textSecondary">
              Choose how you want to start setting up your live video
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <div className={classes.videoSetupItem} onClick={(e) => handleSelectLiveVideoSetup('Cameo')}>
              <AppImg
                src={`${membershipAssetsPath}/svgs/live-video-01.svg`}
                width={96}
                height={66}
                alt="Live Video Icon"
              />
              <br />
              <br />
              <br />
              <Typography variant="h4" color="textSecondary" gutterBottom>
                Cameo
              </Typography>
              <div className={classes.videoSetupItemText}>
                <Typography variant="caption" color="textSecondary">
                  Stream video or share your screen from a single input source, add intro and outro videos.
                </Typography>
              </div>
              <IconButton aria-label="arrow">
                <ArrowForwardOutlinedIcon fontSize="inherit" />
              </IconButton>
            </div>
          </Grid>
          <Grid item xs={6}>
            <div className={classes.videoSetupItem}>
              <AppImg
                src={`${membershipAssetsPath}/svgs/live-video-02.svg`}
                width={96}
                height={66}
                alt="Live Video Icon"
              />
              <br />
              <br />
              <br />
              <Typography variant="h4" color="textSecondary" gutterBottom>
                Studio
              </Typography>
              <div className={classes.videoSetupItemText}>
                <Typography variant="caption" color="textSecondary">
                  Stream video from multiple camera sources, add participants, countdown, intro and outro videos,
                  graphics, and interactive content.
                </Typography>
              </div>
              <Typography variant="h5" color="textSecondary">
                Coming Soon
              </Typography>
              {/* <IconButton aria-label="arrow" onClick={(e) => handleSelectLiveVideoSetup('Studio')}>
                <ArrowForwardOutlinedIcon fontSize="inherit" />
              </IconButton> */}
            </div>
          </Grid>
        </Grid>
      </DialogContent>
    </Dialog>
  )
}

export default LiveVideoSetup
