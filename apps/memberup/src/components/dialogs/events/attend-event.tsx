import AccessTimeIcon from '@mui/icons-material/AccessTime'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import CircularProgress from '@mui/material/CircularProgress'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import useTheme from '@mui/material/styles/useTheme'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import React, { useState } from 'react'

import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { adjustRGBA } from '@memberup/shared/src/libs/color'
import { formatInTimeZone } from '@memberup/shared/src/libs/date-utils'
import { createEventAttendApi } from '@memberup/shared/src/services/apis/event.api'
import { IEvent, IEventAttendee } from '@memberup/shared/src/types/interfaces'
import SVGClose from '@/memberup/components/svgs/close'

const ics = require('ics')

const useStyles = makeStyles((theme) => ({
  root: {
    '& .MuiDialog-paper': {
      borderRadius: 12,
    },
  },
  dialogTitle: {
    borderBottom: 'none',
    padding: 24,
    paddingTop: 40,
    paddingRight: 48,
    paddingBottom: 24,
    textAlign: 'center',
  },
  dialogContent: {
    paddingLeft: 24,
    paddingRight: 24,
    paddingBottom: 24,
  },
}))

const AttendEvent: React.FC<{
  appEvent: IEvent
  open: boolean
  onClose: (e?: IEventAttendee) => void
}> = ({ appEvent, open, onClose }) => {
  const classes = useStyles()
  const mountedRef = useMounted(true)
  const theme = useTheme()
  const [requestAttend, setRequestAttend] = useState(false)
  const eventStartDateTime = appEvent.start_time * 1000
  const eventEndDateTime = appEvent.end_time * 1000
  const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone

  const handleAttend = () => {
    setRequestAttend(true)
    createEventAttendApi(appEvent.id as string)
      .then((res) => {
        if (!mountedRef.current) return
        onClose(res.data.data)
      })
      .catch((err) => {})
      .finally(() => {
        if (!mountedRef.current) return
        setRequestAttend(false)
      })
  }

  return (
    <Dialog
      maxWidth="xs"
      fullWidth={true}
      className={classes.root}
      open={open}
      onClose={() => onClose()}
      PaperProps={{
        onClick: (e) => e.stopPropagation(),
      }}
      aria-labelledby="attend-event-dialog-title"
    >
      <DialogTitle className={classes.dialogTitle} id="attend-event-dialog-title">
        Your seat is saved at {appEvent.title}
        <IconButton
          size="small"
          aria-label="close"
          className="close color03"
          onClick={(e) => {
            e.preventDefault()
            e.stopPropagation()
            onClose()
          }}
        >
          <SVGClose fontSize={16} />
        </IconButton>
      </DialogTitle>
      <DialogContent className={classes.dialogContent}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <div dangerouslySetInnerHTML={{ __html: appEvent.description || '' }}></div>
          </Grid>
          <Grid item xs={12}>
            <Box>
              <Grid className="color03" container spacing={2} alignItems="center">
                <Grid className="d-flex algin-center width-auto" item>
                  <AccessTimeIcon color="inherit" />
                </Grid>
                <Grid item>
                  <Typography color="text.disabled" variant="body1">
                    {formatInTimeZone(eventStartDateTime, timeZone, 'E, d LLLL')}
                    &nbsp;&nbsp;&nbsp;•&nbsp;&nbsp;&nbsp;
                    {formatInTimeZone(eventStartDateTime, timeZone, 'h:mm a')}
                    &nbsp;To&nbsp;
                    {formatInTimeZone(eventEndDateTime, timeZone, 'h:mm a z')}
                  </Typography>
                </Grid>
              </Grid>
            </Box>
          </Grid>
          <Grid item xs={12}>
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Button
                  className="round-small"
                  variant="outlined"
                  color="inherit"
                  disabled={requestAttend}
                  fullWidth
                  onClick={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    onClose()
                  }}
                >
                  Cancel
                </Button>
              </Grid>
              <Grid item xs={6}>
                <Button
                  className="round-small"
                  sx={{ backgroundColor: adjustRGBA(theme.palette.primary.main, 0.8) }}
                  variant="contained"
                  disabled={requestAttend}
                  fullWidth
                  onClick={(e) => {
                    handleAttend()
                  }}
                >
                  {requestAttend ? <CircularProgress size={16} /> : 'Attend'}
                </Button>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </DialogContent>
    </Dialog>
  )
}

export default React.memo(AttendEvent)
