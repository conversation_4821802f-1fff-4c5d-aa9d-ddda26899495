import Button from '@mui/material/Button'
import Checkbox from '@mui/material/Checkbox'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import FormControlLabel from '@mui/material/FormControlLabel'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import useTheme from '@mui/material/styles/useTheme'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import React, { useState } from 'react'

import { adjustRGBA } from '@memberup/shared/src/libs/color'
import SVGClose from '@/memberup/components/svgs/close'

const useStyles = makeStyles((theme) => ({
  root: {
    '& .MuiDialog-paper': {
      borderRadius: 12,
    },
    '& .MuiDialogTitle-root .MuiTypography-h6': {
      fontSize: 16,
    },
  },
  dialogTitle: {
    borderBottom: 'none',
    padding: 24,
    paddingRight: 48,
  },
  dialogContent: {
    paddingLeft: 36,
    paddingRight: 36,
    paddingTop: 24,
    paddingBottom: 24,
  },
}))

const DeleteUser: React.FC<{
  open: boolean
  onProceed: (ban?: boolean) => void
  onClose: () => void
  userRole: 'admin' | 'member'
}> = ({ open, onProceed, onClose, userRole }) => {
  const classes = useStyles()
  const theme = useTheme()
  const [ban, setBan] = useState(false)

  return (
    <Dialog
      maxWidth="xs"
      fullWidth={true}
      className={classes.root}
      open={open}
      onClose={() => onClose()}
      PaperProps={{
        onClick: (e) => e.stopPropagation(),
      }}
      aria-labelledby="delete-user-dialog-title"
    >
      <DialogTitle className={classes.dialogTitle} id="delete-user-dialog-title">
        <IconButton
          size="small"
          aria-label="close"
          className="close color03"
          onClick={(e) => {
            e.preventDefault()
            e.stopPropagation()
            onClose()
          }}
        >
          <SVGClose fontSize={16} />
        </IconButton>
      </DialogTitle>
      <DialogContent className={classes.dialogContent}>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Typography variant="body1" gutterBottom>
              Are you sure you want to delete this {userRole}?
            </Typography>
          </Grid>
          <Grid item xs={12}>
            <Typography
              variant="body1"
              gutterBottom
              sx={{
                lineHeight: '1.5',
                fontStyle: 'italic',
              }}
            >
              If they have any active subscriptions, they will be immediately cancelled.
            </Typography>
          </Grid>
          <Grid item xs={12}>
            <FormControlLabel
              control={<Checkbox checked={ban} onChange={(e) => setBan(e.target.checked)} />}
              label={`Prevent ${userRole} from rejoining (ban ${userRole})`}
            />
            <br />
            <br />
          </Grid>
          <Grid item xs={6}>
            <Button
              className="round-small"
              variant="outlined"
              color="inherit"
              fullWidth
              onClick={(e) => {
                e.preventDefault()
                e.stopPropagation()
                onClose()
              }}
            >
              Cancel
            </Button>
          </Grid>
          <Grid item xs={6}>
            <Button
              className="round-small"
              sx={{ backgroundColor: adjustRGBA(theme.palette.primary.main, 0.8) }}
              variant="contained"
              color="primary"
              fullWidth
              onClick={(e) => {
                e.preventDefault()
                e.stopPropagation()
                onProceed(ban)
              }}
            >
              Yes
            </Button>
          </Grid>
        </Grid>
      </DialogContent>
    </Dialog>
  )
}

export default React.memo(DeleteUser)
