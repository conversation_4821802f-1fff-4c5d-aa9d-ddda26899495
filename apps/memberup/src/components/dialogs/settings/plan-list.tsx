import CheckIcon from '@mui/icons-material/Check'
import Button from '@mui/material/Button'
import Stack from '@mui/material/Stack'
import { useTheme } from '@mui/material/styles'
import React from 'react'

import SVGFatCheck from '../../svgs/fat-check'
import { MAX_NUMBER_OF_COURSES, MAX_NUMBER_OF_SPACES } from '@/shared-settings/plans'

const PlanList = ({ plans, onChange, value }) => {
  const theme = useTheme()
  const onPlanChange = (newValue) => {
    if (newValue !== value) {
      if (onChange) {
        onChange(newValue)
      }
    }
  }

  return (
    <Stack direction={'row'} gap={16} sx={{ justifyContent: 'center' }}>
      {plans.map((plan) => (
        <Button
          onClick={() => onPlanChange(plan.name)}
          key={plan.name}
          variant={'outlined'}
          sx={
            plan.name === value
              ? {
                  borderRadius: '14px',
                  borderWidth: '1px',
                  borderColor: theme.palette.mode === 'dark' ? 'white' : 'black',
                  backgroundColor: theme.palette.mode === 'dark' ? '#202125' : '#f6f7f8',
                }
              : {
                  borderRadius: '14px',
                  borderWidth: '1px',
                  borderColor: theme.palette.mode === 'dark' ? 'gray' : '#ebebec',
                }
          }
        >
          <Stack sx={{ textAlign: 'left', width: '184px', padding: '15px 5px' }} direction={'column'}>
            {plan.name === value && (
              <CheckIcon
                style={{
                  position: 'absolute',
                  right: 10,
                  top: 10,
                  color: theme.palette.mode === 'dark' ? '#fff' : theme.palette.primary.dark,
                }}
              />
            )}
            <span
              style={{
                marginBottom: '5px',
                fontFamily: 'Graphik Semibold',
                fontWeight: 500,
                fontSize: '13px',
                color: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.87)' : '#000000',
              }}
            >
              {plan.displayName}
            </span>
            <span
              style={{
                marginBottom: '5px',
                fontFamily: 'Graphik Medium',
                fontWeight: 500,
                fontSize: '12px',
                color: theme.palette.mode === 'dark' ? 'rgb(141, 148, 163)' : '#585D66',
              }}
            >
              Billed Annually
            </span>
            <span
              style={{
                color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000',
                fontFamily: 'Graphik SemiBold',
                fontWeight: 500,
                fontSize: '20px',
                lineHeight: '32px',
                marginBottom: '10px',
              }}
            >
              ${plan.monthlyPrice}
              <span
                style={{
                  fontFamily: 'Graphik Medium',
                  fontWeight: 500,
                  fontSize: '12px',
                  color: theme.palette.mode === 'dark' ? 'rgb(141, 148, 163)' : '#585D66',
                }}
              >
                &nbsp;/ month
              </span>
            </span>
            <span
              style={{
                marginBottom: '10px',
                fontFamily: 'Graphik Medium',
                fontWeight: 500,
                fontSize: '13px',
                color: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.87)' : '#000000',
              }}
            >
              <SVGFatCheck
                styles={{
                  color: '#AEE78B',
                }}
              />
              &nbsp;
              {plan.maxNumOfSpaces === MAX_NUMBER_OF_SPACES ? 'Unlimited' : plan.maxNumOfSpaces} spaces{' '}
            </span>
            <span
              style={{
                fontFamily: 'Graphik Medium',
                fontWeight: 500,
                fontSize: '13px',
                color: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.87)' : '#000000',
              }}
            >
              <SVGFatCheck
                styles={{
                  color: '#AEE78B',
                }}
              />
              &nbsp;
              {plan.maxNumOfCourses === MAX_NUMBER_OF_COURSES ? 'Unlimited' : plan.maxNumOfCourses} courses
            </span>
          </Stack>
        </Button>
      ))}
    </Stack>
  )
}

export default PlanList
