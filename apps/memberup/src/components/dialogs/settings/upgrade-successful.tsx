import CheckIcon from '@mui/icons-material/Check'
import Box from '@mui/material/Box'
import { useTheme } from '@mui/material/styles'
import Typography from '@mui/material/Typography'

import { AppImg } from '@memberup/shared/src/components/common/media/image'

export const UpgradeSuccessful = () => {
  const theme = useTheme()
  return (
    <Box
      data-cy={'payment-confirmed-block'}
      sx={{
        position: 'relative',
      }}
    >
      <Box className="text-center" sx={{ position: 'relative', maxWidth: 450, m: 'auto', pt: '45px', pb: '24px' }}>
        <Box
          sx={{
            position: 'absolute',
            left: 0,
            right: 0,
            top: 0,
            borderBottomLeftRadius: '35%',
            borderBottomRightRadius: '35%',
            bottom: 0,
            backgroundImage:
              'linear-gradient(180deg, rgba(249, 115, 0, 1) 0%, rgba(217, 0, 135, 1) 52%, rgba(49, 0, 200, 1) 100%)',
            filter: 'blur(160px)',
            opacity: 0.36,
            zIndex: 0,
          }}
        />
        <Box
          sx={{
            position: 'relative',
            maxWidth: 450,
            m: 'auto',
          }}
        >
          <Box
            className="text-center"
            sx={{
              width: 64,
              height: 64,
              backgroundColor: '#49CC98',
              borderRadius: '50%',
              p: '14px',
              m: 'auto',
            }}
          >
            <CheckIcon sx={{ color: '#000000', fontSize: 36 }} />
          </Box>
          <Typography
            align="center"
            color="inherit"
            variant="h4"
            gutterBottom
            sx={{ mt: '24px', color: theme.palette.mode === 'dark' ? '#fff' : '#000' }}
          >
            Payment confirmed!
          </Typography>
          <Typography
            align="center"
            color="inherit"
            variant="h4"
            sx={{ color: theme.palette.mode === 'dark' ? '#fff' : '#000' }}
          >
            You just leveled up 👏
          </Typography>
        </Box>
      </Box>
    </Box>
  )
}
