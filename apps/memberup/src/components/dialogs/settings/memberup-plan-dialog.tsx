import Box from '@mui/material/Box'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import Slide, { SlideProps } from '@mui/material/Slide'
import { makeStyles } from '@mui/styles'
import React from 'react'

import { IUser } from '@memberup/shared/src/types/interfaces'
import MemberupPlan from '@/memberup/components/stripe/memberup-plan'

const useStyles = makeStyles((theme) => ({
  root: {
    width: '100%',
    maxWidth: '100%',
    minWidth: 380,
    marginLeft: 'auto',
    '& .MuiDialog-paper': {
      borderRadius: 12,
    },
    '& .MuiDialog-paperFullWidth': {
      width: '100%',
      height: '100%',
      maxHeight: '100%',
      margin: 0,
    },
    '& .MuiDialogTitle-root .MuiTypography-h6': {
      fontSize: 16,
    },
  },
  dialogTitle: {
    borderBottom: 'none',
    padding: '24px 24px 8px 24px',
  },
  dialogContent: {
    minHeight: 280,
    lineHeight: 1,
    padding: 24,
  },
  dialogAction: {
    backgroundColor: 'transparent',
  },
}))

// eslint-disable-next-line react/display-name
const Transition = React.forwardRef<unknown, SlideProps>((props, ref) => (
  <Slide direction="left" {...props} ref={ref} />
))

const MemberupPlanDialog: React.FC<{ open: boolean; onClose: () => void; member?: IUser }> = ({
  member,
  open,
  onClose,
}) => {
  const classes = useStyles()

  return (
    <Dialog
      maxWidth="xl"
      fullWidth={true}
      className={classes.root}
      open={open}
      onClose={onClose}
      TransitionComponent={Transition}
      TransitionProps={{
        in: open,
        timeout: {
          appear: 800,
          enter: 800,
          exit: 500,
        },
      }}
      aria-labelledby="memberup-plan-dialog-title"
    >
      <DialogTitle className={classes.dialogTitle} id="memberup-plan-dialog-title">
        {/* Choose Plan
        <IconButton
          size="small"
          aria-label="close"
          className="close large color02"
          onClick={onClose}
        >
          <CloseIcon fontSize="inherit" />
        </IconButton> */}
      </DialogTitle>
      <DialogContent className={classes.dialogContent}>
        <Box sx={{ maxWidth: '1280px', margin: 'auto' }}>
          <MemberupPlan />
        </Box>
      </DialogContent>
    </Dialog>
  )
}

export default MemberupPlanDialog
