import Button from '@mui/material/Button'
import Grid from '@mui/material/Grid'
import Table from '@mui/material/Table'
import TableBody from '@mui/material/TableBody'
import TableCell from '@mui/material/TableCell'
import TableContainer from '@mui/material/TableContainer'
import TableHead from '@mui/material/TableHead'
import TableRow from '@mui/material/TableRow'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import React, { useEffect, useState } from 'react'
import InfiniteScroll from 'react-infinite-scroll-component'

import { AppPaymentMethodNumber } from '@memberup/shared/src/components/common/payment-method-number'
import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { formatDate } from '@memberup/shared/src/libs/date-utils'
import { numberToCurrency } from '@memberup/shared/src/libs/numeric-utils'
import { getStripePayoutsApi } from '@memberup/shared/src/services/apis/stripe.api'
import { IUser } from '@memberup/shared/src/types/interfaces'

const useStyles = makeStyles((theme) => ({
  root: {
    overflow: 'auto',
    maxHeight: 360,
    '& .MuiTableCell-root': {
      color: theme.palette.text.disabled,
    },
  },
}))

const PayoutsTable: React.FC<{ user: IUser }> = ({ user }) => {
  const classes = useStyles()
  const mountedRef = useMounted(true)
  const [requestPayouts, setRequestPayouts] = useState(false)
  const [allView, setAllView] = useState(false)
  const [payouts, setPayouts] = useState({
    data: [],
    hasMore: false,
    startingAfter: null,
  })

  const fetchMorePayouts = async (startingAfter?: string) => {
    try {
      if (requestPayouts) return
      setRequestPayouts(true)
      getStripePayoutsApi(true, {
        limit: 6,
        starting_after: startingAfter,
      })
        .then((res) => {
          if (res.data.success) {
            setPayouts((prevValue) => ({
              data: startingAfter ? res.data.data.data : prevValue.data.concat(res.data.data.data),
              hasMore: res.data.data.has_more,
              startingAfter: res.data.data.data.length ? res.data.data.data[res.data.data.data.length - 1].id : null,
            }))
          }
        })
        .finally(() => {
          if (!mountedRef.current) return
          setRequestPayouts(false)
        })
    } catch (err: any) {
      if (!mountedRef.current) return
      setRequestPayouts(false)
    }
  }

  useEffect(() => {
    fetchMorePayouts()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return (
    <div className={classes.root}>
      <Grid container alignItems="center">
        <Grid item xs>
          <Typography variant="subtitle1">
            <b>Payment History</b>
          </Typography>
        </Grid>
        <Grid item>
          <Button className="no-padding" variant="text" color="primary" onClick={() => setAllView(true)}>
            View All
          </Button>
        </Grid>
      </Grid>
      <InfiniteScroll
        dataLength={payouts.data.length}
        next={() => fetchMorePayouts(payouts.startingAfter)}
        hasMore={payouts.hasMore && allView}
        loader={<div className="text-center">Loading...</div>}
        scrollableTarget="profile-dialog-content"
      >
        <TableContainer>
          <Table stickyHeader aria-label="sticky table">
            <TableHead>
              <TableRow>
                <TableCell>Transaction Date</TableCell>
                <TableCell>Amount</TableCell>
                <TableCell>Payment method</TableCell>
                <TableCell>Status</TableCell>
                <TableCell></TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {payouts.data.map((row, index) => (
                <TableRow key={`row-${index}`} hover role="checkbox">
                  <TableCell>{formatDate({ date: row.created, format: 'MMM dd yyyy, K:mm aa z' })}</TableCell>
                  <TableCell>
                    <b className="color01">{numberToCurrency(row.amount)}</b>
                  </TableCell>
                  <TableCell>
                    <AppPaymentMethodNumber number={4242} />
                  </TableCell>
                  <TableCell>
                    <div className="status round-small color01 background-color11">{row.status}</div>
                  </TableCell>
                  <TableCell></TableCell>
                </TableRow>
              ))}
              {!requestPayouts && !payouts.data.length && (
                <TableRow>
                  <TableCell className="text-center" colSpan={5}>
                    No Payments
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </InfiniteScroll>
    </div>
  )
}

export default PayoutsTable
