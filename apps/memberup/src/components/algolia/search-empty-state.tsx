import Box from '@mui/material/Box'
import ListItem from '@mui/material/ListItem'
import ListItemText from '@mui/material/ListItemText'
import Typography from '@mui/material/Typography'

const SearchEmptyState = () => {
  return (
    <Box>
      <Typography
        color="secondary"
        sx={{
          fontSize: '1.3rem',
          textAlign: 'center',
          fontWeight: 'bold',
          fontFamily: 'Graphik Regular',
          color: '#000',
        }}
      >
        No results found
      </Typography>

      <ListItem>
        <ListItemText
          primary="Try adjusting your search or filter to find what you're looking for."
          primaryTypographyProps={{ color: '#363636', textAlign: 'center' }}
        />
      </ListItem>
    </Box>
  )
}

export default SearchEmptyState
