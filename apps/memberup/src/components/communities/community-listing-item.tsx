import { Box, Stack, Typography } from '@mui/material'
import { SxProps, Theme } from '@mui/material/styles'
import { VISIBILITY_ENUM } from '@prisma/client'

import { Favicon } from '@/components/community/favicon'
import useAppTheme from '@/memberup/components/hooks/use-app-theme'
import { IMembership } from '@/shared-types/interfaces'

interface CommunityListingItemProps {
  membership: IMembership
  sx?: SxProps<Theme>
}

export default function CommunityListingItem({ membership, sx = [] }: CommunityListingItemProps) {
  const { isDarkTheme } = useAppTheme()

  return (
    <Stack direction="row" sx={sx}>
      <Favicon
        cropArea={membership.membership_setting.favicon_crop_area}
        src={membership.membership_setting.favicon}
        communityName={membership.name}
        width={48}
        height={48}
      />
      <Box sx={{ flexGrow: 1, p: '10px 0 0 12px' }}>
        <Typography
          className="demi"
          variant="h5"
          sx={{
            color: isDarkTheme ? 'var(--body-copy)' : 'var(--font-light-ui-black)',
            fontSize: 16,
            lineHeight: '24px',
            mb: '4px',
          }}
        >
          {membership.name}
        </Typography>
        <Typography
          sx={{
            fontSize: 13,
            lineHeight: '16px',
            color: isDarkTheme ? 'var(--ui-dark-800)' : 'var(--font-light-ui-gray)',
          }}
        >
          {membership.membership_setting.visibility === VISIBILITY_ENUM.private ? 'Private ' : 'Public '}• 500 members
        </Typography>
      </Box>
    </Stack>
  )
}
