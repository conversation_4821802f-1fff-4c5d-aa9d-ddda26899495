import Box from '@mui/material/Box'
import IconButton from '@mui/material/IconButton'
import useTheme from '@mui/material/styles/useTheme'
import { saveAs } from 'file-saver'
import Image from 'next/image'

import { THEME_MODE_ENUM } from '@memberup/shared/src/types/enum'
import { DownloadFileHandler } from '@memberup/shared/src/types/types'
import MediaFiles from '@/memberup/components/common/media-files'
import SVGDownload from '@/memberup/components/svgs/download'

export function processAttachments(feed: any) {
  const result = feed.attachments
    .filter((item) => !item.title_link)
    .reduce((acc, attachment) => {
      const mimetype = attachment.mimetype || attachment.type || null
      if (['image'].includes(mimetype)) {
        if (!acc['image']) {
          acc['image'] = []
        }
        acc['image'].push(attachment)
      } else if (['video', 'gif'].includes(mimetype)) {
        if (!acc['media']) {
          acc['media'] = []
        }
        acc['media'].push(attachment)
      } else if (mimetype === 'pdf') {
        if (!acc['pdf']) {
          acc['pdf'] = []
        }
        acc['pdf'].push(attachment)
      } else if (['audio', 'text', 'other', 'doc'].includes(mimetype)) {
        if (!acc['other']) {
          acc['other'] = []
        }
        acc['other'].push(attachment)
      }
      return acc
    }, {})
  return result
}

const AppMessagingAttachmentsView = ({ feed, isMine, handleRemovePreview }) => {
  const theme = useTheme()
  const isLightTheme = theme.palette.mode === THEME_MODE_ENUM.light
  const attachments = processAttachments(feed)
  const images = attachments['image'] || []
  const handleDownload: DownloadFileHandler = (file) => {
    if (file.url) {
      saveAs(file.url, file.filename)
    }
  }

  return (
    <>
      {images.map((item, index) => {
        return (
          <div key={index}>
            <Box
              key={`image-${index}`}
              sx={{
                position: 'relative',
                borderRadius: '16px',
                borderBottomLeftRadius: !isMine && '2px',
                borderBottomRightRadius: isMine && '2px',
                display: 'flex',
                flexDirection: 'column-reverse',
                lineHeight: '8px',
                overflow: 'hidden',
                '& .MuiIconButton-root': {
                  visibility: 'hidden',
                },
                '&:hover': {
                  '& .MuiIconButton-root': {
                    visibility: 'visible',
                  },
                },
              }}
            >
              <Image
                width={296}
                height={296}
                id={item.url}
                src={item.thumbnail || item.url}
                alt={`Attachment ${item.filename}`}
                priority
                style={{
                  height: '100%',
                  width: '100%',
                  //maxWidth: '296px',
                }}
              />
              <IconButton
                sx={{
                  position: 'absolute',
                  bottom: 0,
                  right: '2px',
                  color: isLightTheme ? '#000000' : '#ffffff',
                  zIndex: 1,
                }}
                aria-label="download"
                onClick={() => handleDownload(item)}
              >
                <SVGDownload />
              </IconButton>
            </Box>
          </div>
        )
      })}
      <MediaFiles
        files={[...(attachments['media'] || []), ...(attachments['pdf'] || []), ...(attachments['other'] || [])]}
        handleDownloadFile={handleDownload}
        verticalOnly={true}
      />
    </>
  )
}
export default AppMessagingAttachmentsView
