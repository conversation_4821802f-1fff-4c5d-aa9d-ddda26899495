import { useEffect, useRef } from 'react'
import { useChatContext } from 'stream-chat-react'

import { filterSpaces } from '@/lib/utils'
import { addMessage, deleteMessage, updateMessage } from '@/memberup/store/features/feedAggregationSlice'
import { selectActiveChannels } from '@/memberup/store/features/spaceSlice'
import { useAppSelector } from '@/memberup/store/hooks'
import { useAppDispatch } from '@/memberup/store/store'
import member from '@/src/components/svgs/member'
import { selectMembership } from '@/src/store/features/membershipSlice'
import { selectUser } from '@/src/store/features/userSlice'

export const useAppStreamChannelListener = () => {
  const { client: streamChatClient } = useChatContext()
  const spaces = useAppSelector((state) => selectActiveChannels(state))
  const dispatch = useAppDispatch()
  const listenersRef = useRef([])
  const membership = useAppSelector((state) => selectMembership(state))
  const user = useAppSelector((state) => selectUser(state))

  useEffect(() => {
    if (!streamChatClient?.user || spaces.length === 0) {
      return
    }

    async function startListening() {
      const filteredSpaces = filterSpaces(spaces, membership, user)

      const selectedChannelsIds = filteredSpaces.map((space) => space.id)
      const filter = { id: { $in: selectedChannelsIds } }

      const sort: any = { id: -1 }
      // Query for those channels, automatically watching them for the currently connected user

      const channels = await streamChatClient.queryChannels(filter, sort, {
        watch: true,
        limit: 30,
      })

      function findSpaceById(spaces: any[], spaceId: string) {
        return spaces.find((s: any) => s.id === spaceId)
      }

      function isFeedMessage(event) {
        return event.channel_id && event.message?.type === 'regular'
      }

      function attachChannelToMessage(message, messageChannel) {
        return {
          ...message,
          channel: { name: messageChannel.name, id: messageChannel.id },
        }
      }

      for (let channel of channels) {
        let listener
        listener = channel.on('message.new', (event) => {
          if (!isFeedMessage(event)) return
          const messageChannel = findSpaceById(spaces, event.channel_id)
          if (!messageChannel) return

          // Publish message to the space and to the aggregated feed (Community).
          const message = attachChannelToMessage(event.message, messageChannel)
          dispatch(addMessage({ id: event.channel_id, message: message }))
          dispatch(addMessage({ id: 'community', message: message }))
        })
        listenersRef.current.push(listener)

        listener = channel.on('message.updated', (event) => {
          if (event.channel_id && event.message?.type === 'regular') {
            const messageChannel = spaces.find((s) => s.id === event.channel_id)
            if (messageChannel) {
              const message = attachChannelToMessage(event.message, messageChannel)
              dispatch(updateMessage({ id: event.channel_id, message: message }))
              dispatch(updateMessage({ id: 'community', message: message }))
            }
          }
        })
        listenersRef.current.push(listener)

        listener = channel.on('message.deleted', (event) => {
          if (event.channel_id) {
            const messageChannel = findSpaceById(spaces, event.channel_id)
            if (!messageChannel) return
            // Delete from the space and from the aggregated feed (Community).
            dispatch(deleteMessage({ id: event.channel_id, message: event.message }))
            dispatch(deleteMessage({ id: 'community', message: event.message }))
          }
        })
        listenersRef.current.push(listener)
      }
    }

    startListening()

    return () => {
      listenersRef.current.forEach((listener) => listener?.unsubscribe?.())
    }
  }, [streamChatClient, spaces])
}
