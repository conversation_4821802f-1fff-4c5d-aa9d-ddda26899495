import { Expand<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@mui/icons-material'
import MoreHorizIcon from '@mui/icons-material/MoreHoriz'
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Button,
  Grid,
  IconButton,
  ListItem,
  Menu,
  Skeleton,
  Text<PERSON>ield,
  Typography,
} from '@mui/material'
import Image from 'next/image'
import React, { useEffect, useRef, useState } from 'react'
import { Controller } from 'react-hook-form'
import { Type } from 'react-toastify/dist/utils'

import AppTooltip from '../common/app-tooltip'
import VisuallyHiddenInput from '../common/hidden-input'
import ImageCropDialog from '../dialogs/image-crop-dialog'
import { AppDropzone } from '@memberup/shared/src/components/common/app-dropzone'
import AppMuxPlayer from '@memberup/shared/src/components/common/app-mux-player'
import { VIDEO_TRANSCRIPTION_GENERAL_ERROR_MSG } from '@memberup/shared/src/types/consts'
import { useStore } from '@/hooks/useStore'
import useAppTheme from '@/memberup/components/hooks/use-app-theme'
import useUploadFiles from '@/memberup/components/hooks/use-upload-files'
import SVGEditNew from '@/memberup/components/svgs/edit-new'
import SVGPhotoAdd from '@/memberup/components/svgs/photo-add'
import SVGPhoto from '@/shared-components/svgs/photo'

const thumbnailPlaceholderDimensions = {
  width: '571px',
  height: '320px',
}

const TRANSCRIPT_NOT_READY_MSG = 'Transcriptions are not ready yet. As soon as they are, you will see them here.'
const formatTranscript = (transcript) => {
  if (!transcript) {
    return <></>
  }
  // Split the transcript into sentences
  const sentences = transcript?.match(/[^.!?]+[.!?]+/g) || []

  // Group sentences into paragraphs of 3 sentences each
  const paragraphs = []
  for (let i = 0; i < sentences.length; i += 3) {
    paragraphs.push(sentences.slice(i, i + 3).join(' '))
  }

  // Render the paragraphs
  return paragraphs.map((paragraph, index) => (
    <p key={index}>
      {paragraph}
      <br />
      <br />
    </p>
  ))
}

const ShowVideoMedia = ({
  uploadStatus,
  lessonFile,
  control,
  selectedLessonId,
  setValue,
  setHasUnsavedChanges,
  setIsVideoLoaded,
  onVideoDownload,
  videoDownloadReady,
  setVideoDownloadReady,
  sections,
  setSections,
  selectedSectionId,
}) => {
  const [menuAnchorEl, setMenuAnchorEl] = useState(null)
  const [videoAnchorEl, setVideoAnchorEl] = useState(null)
  const membership = useStore((state) => state.community.membership)
  const { uploadProgress, uploadedFiles, initUploadFiles, handleUploadFiles } = useUploadFiles('library', membership.id)
  const [openImageCropper, setOpenImageCropper] = useState(false)
  const [thumbnailFile, setThumbnailFile] = useState(null)
  const [previousThumbnailFile, setPreviousThumbnailFile] = useState(null)
  const fileInputRef = useRef(null)

  useEffect(() => {
    if (uploadProgress === 100) {
      if (uploadedFiles) {
        setValue('thumbnail_url', uploadedFiles[0]?.url)
      }
    }
  }, [uploadedFiles, uploadProgress])
  const handleDropThumbnail = async (f) => {
    setPreviousThumbnailFile(thumbnailFile)
    // Upload the file to Cloudinary
    initUploadFiles()
    const uploadedFiles = await handleUploadFiles([f], 'Cloudinary')
    // Set the URL of the uploaded file to the thumbnailFile state
    setThumbnailFile(uploadedFiles[0]?.url)
    // Open the ImageCropDialog
    setOpenImageCropper(true)
  }

  const handleMenuClose = () => {
    setMenuAnchorEl(null)
  }
  const handleVideoMenuClose = (event) => {
    setVideoAnchorEl(null)
    event.stopPropagation()
  }
  const handleRemoveThumbnail = () => {
    setValue('thumbnail_url', null)
    setHasUnsavedChanges(true)
    setThumbnailFile(null)
    setSections((prevSections) => {
      const updatedSections = { ...prevSections }
      updatedSections[selectedSectionId].ContentLibraryCourseLesson[selectedLessonId].thumbnail_url = null
      return updatedSections
    })
    handleMenuClose()
  }

  const handleMenuClick = (event) => {
    setMenuAnchorEl(event.currentTarget)
  }

  const handleVideoMenuClick = (event) => {
    setVideoAnchorEl(event.currentTarget)
  }

  const { theme, isDarkTheme } = useAppTheme()

  return (
    <Box>
      {uploadStatus === 'processing' ? (
        <Skeleton
          variant="rectangular"
          height={'50vh'}
          sx={{
            marginBottom: '12px',
            borderRadius: '12px',
            width: '100%',
            height: '50vh',
          }}
        />
      ) : (
        <>
          <Box
            sx={{
              position: 'relative',
              width: '100%',
              height: 'auto',
              overflow: 'hidden',
              borderRadius: '12px 12px 0px 0px',
            }}
          >
            <AppMuxPlayer
              autoPlay={false}
              asset={lessonFile}
              streamType="on-demand"
              poster={
                thumbnailFile?.croppedImg?.url ||
                sections?.[selectedSectionId]?.ContentLibraryCourseLesson?.[selectedLessonId]?.thumbnail_url
              }
              wrapperStyle={{
                width: '100%',
                height: '100%',
              }}
              playerStyle={{
                width: '100%',
                height: '100%',
                '--media-object-fit': 'contain',
                aspectRatio: '16 / 9',
                backgroundColor: '#000000',
              }}
              onEnded={(e) => {}}
            />
          </Box>
          <Box
            sx={{
              '& .MuiAccordion-root': {
                borderRadius: '0px 0px 16px 16px !important',
              },
              '& .MuiAccordionSummary-expandIconWrapper': {
                position: 'absolute',
                left: '158px',
                color: theme.palette.text.disabled,
              },
            }}
          >
            <Accordion
              sx={{
                backgroundColor: isDarkTheme ? '#212124' : '#ffffff',
                padding: '16px 20px',
                boxShadow: isDarkTheme ? 'none' : '0px 5px 10px rgba(0, 0, 0, 0.1)',
              }}
            >
              <AccordionSummary
                expandIcon={<ExpandMore sx={{ maxWidth: '21px' }} />}
                aria-controls="panel1a-content"
                id="panel1a-header"
              >
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                  }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: theme.palette.action.disabledBackground,
                      borderRadius: '12px',
                      width: '32px',
                      height: '32px',
                      p: '10px',
                    }}
                  >
                    <SVGEditNew />
                  </Box>
                  <Typography
                    sx={{
                      ml: '8px',
                      fontFamily: 'Graphik Semibold',
                      fontSize: '14px',
                    }}
                  >
                    Customize media
                  </Typography>
                </Box>
                <Controller
                  name="mediaOptions"
                  control={control}
                  render={({ field }) => (
                    <>
                      <IconButton
                        onClick={(event) => {
                          event.stopPropagation()
                          handleVideoMenuClick(event)
                        }}
                        sx={{
                          position: 'absolute',
                          right: 0,
                          bottom: -2,
                          backgroundColor: theme.palette.action.disabledBackground,
                          color: '#8d94a2',
                        }}
                      >
                        <MoreHorizIcon sx={{ width: '21px', height: '21px' }} />
                      </IconButton>
                      <Menu
                        anchorEl={videoAnchorEl}
                        keepMounted
                        open={Boolean(videoAnchorEl)}
                        onClose={handleVideoMenuClose}
                        sx={{
                          '& .MuiPaper-root': {
                            borderRadius: '12px',
                            boxShadow: '1px 1px 5px 0 rgba(0, 0, 0, 0.2)',
                            border: isDarkTheme ? 'solid 1px #2a2b30' : 'solid 1px #d7d9da',
                            backgroundColor: isDarkTheme ? '#17171a' : '#ffffff',
                            backgroundImage: 'unset',
                            padding: '6px',
                          },
                          '& .MuiList-root': {
                            padding: '0px',
                          },
                        }}
                      >
                        <ListItem
                          sx={{
                            justifyContent: 'left',
                            fontFamily: 'Graphik Medium',
                            fontSize: '13px',
                            height: '40px',
                            margin: '0px',
                            cursor: 'pointer',
                            '&:hover': {
                              backgroundColor: isDarkTheme ? '#1b1b1f' : '#f8f8f8',
                              borderRadius: '12px',
                            },
                          }}
                          onClick={(e) => {
                            if (!videoDownloadReady) return
                            onVideoDownload(e)
                            handleVideoMenuClose(e)
                          }}
                          disabled={!videoDownloadReady}
                        >
                          {videoDownloadReady ? 'Download Video' : 'Generating Download'}
                        </ListItem>
                        <ListItem
                          sx={{
                            justifyContent: 'left',
                            height: '40px',
                            fontFamily: 'Graphik Medium',
                            fontSize: '13px',
                            margin: '0px',
                            cursor: 'pointer',
                            color: theme.palette.error.main,
                            '&:hover': {
                              backgroundColor: isDarkTheme ? '#1b1b1f' : '#f8f8f8',
                              borderRadius: '12px',
                            },
                          }}
                          onClick={(e) => {
                            e.stopPropagation()
                            setHasUnsavedChanges(true)
                            setIsVideoLoaded(false)
                            setValue('media_file', null)
                            setValue('media_transcript', null)
                            handleVideoMenuClose(e)
                            setVideoDownloadReady(false)
                          }}
                        >
                          Remove Video
                        </ListItem>
                      </Menu>
                    </>
                  )}
                />
              </AccordionSummary>
              <AccordionDetails>
                <Grid
                  container
                  sx={{
                    mt: '24px',
                    display: 'flex',
                    justifyContent: 'space-around',
                  }}
                >
                  <Grid xs={5}>
                    <Typography
                      sx={{
                        fontFamily: 'Graphik Medium',
                        fontSize: '14px',
                        mb: 2,
                      }}
                    >
                      Video Thumbnail
                    </Typography>
                    <Controller
                      name="thumbnail_url"
                      control={control}
                      render={({ field }) => {
                        if (field?.value) {
                          return (
                            <Box
                              sx={{
                                width: '100%',
                                height: '156px',
                                minHeight: '154px',
                                position: 'relative',
                              }}
                            >
                              <Button
                                onClick={handleMenuClick}
                                variant="outlined"
                                sx={{
                                  zIndex: 2,
                                  position: 'absolute',
                                  bottom: '8px',
                                  right: '8px',
                                  margin: '0px',
                                  height: '32px',
                                  width: '32px',
                                  backgroundColor: isDarkTheme ? 'rgb(23, 23, 26)' : '#F3F5F5',
                                  borderColor: isDarkTheme ? '#F3F5F5' : 'rgb(23, 23, 26)',
                                  borderRadius: 12,
                                  minHeight: '32px',
                                  minWidth: '32px',
                                  color: theme.palette.text.disabled,
                                  '&:hover': {
                                    backgroundColor: isDarkTheme ? '#29292c' : '#F3F5F5',
                                    borderColor: isDarkTheme ? '#F3F5F5' : '#29292c',
                                  },
                                  '& svg': {
                                    maxWidth: '21px',
                                  },
                                }}
                              >
                                <MoreHoriz />
                              </Button>
                              <Menu
                                anchorEl={menuAnchorEl}
                                keepMounted
                                open={Boolean(menuAnchorEl)}
                                onClose={handleMenuClose}
                                sx={{
                                  '& .MuiPaper-root': {
                                    borderRadius: '12px',
                                    boxShadow: '1px 1px 5px 0 rgba(0, 0, 0, 0.2)',
                                    border: isDarkTheme ? 'solid 1px #2a2b30' : 'solid 1px #d7d9da',
                                    backgroundColor: isDarkTheme ? '#17171a' : '#ffffff',
                                    backgroundImage: 'unset',
                                    padding: '6px',
                                  },
                                  '& .MuiList-root': {
                                    padding: '0px',
                                  },
                                }}
                              >
                                <ListItem
                                  sx={{
                                    justifyContent: 'left',
                                    fontFamily: 'unset',
                                    height: '40px',
                                    margin: '0px',
                                    cursor: 'pointer',
                                    '&:hover': {
                                      backgroundColor: isDarkTheme ? '#1b1b1f' : '#f8f8f8',
                                      borderRadius: '12px',
                                    },
                                  }}
                                  onClick={handleRemoveThumbnail}
                                >
                                  Remove Thumbnail
                                </ListItem>
                              </Menu>
                              <Button
                                component="label"
                                variant="outlined"
                                sx={{
                                  zIndex: 1,
                                  position: 'absolute',
                                  bottom: '8px',
                                  height: '32px',
                                  right: '44px',
                                  backgroundColor: isDarkTheme ? 'rgb(23, 23, 26)' : '#F3F5F5',
                                  borderColor: isDarkTheme ? '#F3F5F5' : 'rgb(23, 23, 26)',
                                  borderRadius: 12,
                                  minHeight: '32px',
                                  '&:hover': {
                                    backgroundColor: isDarkTheme ? '#29292c' : '#F3F5F5',
                                    borderColor: isDarkTheme ? '#F3F5F5' : '#29292c',
                                  },
                                }}
                              >
                                <Typography
                                  sx={{
                                    fontSize: '13px',
                                    fontFamily: 'Graphik Medium',
                                    color: theme.palette.text.primary,
                                    lineHeight: '16px',
                                  }}
                                >
                                  <span
                                    style={{
                                      marginRight: '8px',
                                    }}
                                  >
                                    <SVGPhoto width={13} height={13} />
                                  </span>
                                  Replace
                                </Typography>
                                <VisuallyHiddenInput
                                  ref={fileInputRef}
                                  onChange={(e) => {
                                    //get file
                                    if (e.target?.files?.[0]) {
                                      handleDropThumbnail(e.target.files[0])
                                      setHasUnsavedChanges(true)
                                    }
                                    // Reset the value of the file input
                                    e.target.value = null
                                  }}
                                  type="file"
                                />
                              </Button>
                              {sections?.[selectedSectionId]?.ContentLibraryCourseLesson?.[selectedLessonId]
                                ?.thumbnail_url ? (
                                <Image
                                  src={
                                    sections?.[selectedSectionId]?.ContentLibraryCourseLesson?.[selectedLessonId]
                                      ?.thumbnail_url
                                  }
                                  alt={'Video Thumbnail'}
                                  layout="fill"
                                  style={{
                                    borderRadius: '12px',
                                  }}
                                />
                              ) : null}
                            </Box>
                          )
                        }

                        return (
                          <Box
                            className="border-color02"
                            sx={{
                              display: 'flex',
                              flexDirection: 'column',
                              alignItems: 'center',
                              justifyContent: 'center',
                              height: 154,
                              borderRadius: '12px',
                              borderStyle: 'dashed',
                              borderWidth: '1px',
                              cursor: 'pointer',
                              backgroundColor: theme.palette.mode === 'dark' ? '#202125' : '#eceeef',
                            }}
                          >
                            <AppDropzone
                              file={field?.value}
                              onDropFile={handleDropThumbnail}
                              placeholder={
                                <Box className="text-center">
                                  <Box color={theme.palette.primary.main} marginBottom={'14px'}>
                                    <SVGPhotoAdd width={20} height={20} />
                                  </Box>
                                  <Typography
                                    sx={{
                                      fontSize: '14px',
                                      fontFamily: 'Graphik Medium',
                                      color: theme.palette.text.primary,
                                      lineHeight: '16px',
                                      marginBottom: '8px',
                                    }}
                                  >
                                    Add Thumbnail
                                  </Typography>
                                  <Typography
                                    sx={{
                                      fontSize: '12px',
                                      fontFamily: 'Graphik Regular',
                                      lineHeight: '16px',
                                      color: theme.palette.text.disabled,
                                    }}
                                  >
                                    Recommended size <br />
                                    1392px x 752px
                                  </Typography>
                                </Box>
                              }
                            />
                          </Box>
                        )
                      }}
                    />
                  </Grid>
                  <Grid xs={6} sx={{ height: '100%' }}>
                    <Typography
                      sx={{
                        fontSize: '14px',
                        fontFamily: 'Graphik Medium',
                        color: theme.palette.text.primary,
                        lineHeight: '16px',
                        mb: 2,
                      }}
                    >
                      Transcript generated by
                      <AppTooltip
                        leaveDelay={200}
                        title={
                          <Typography sx={{ fontFamily: 'Graphik Regular', fontSize: '13px' }}>
                            Luna is your AI-powered community assistant
                          </Typography>
                        }
                        placement="top"
                      >
                        <Image
                          src="/assets/default/images/luna.png"
                          alt={'Luna'}
                          width={51}
                          height={14}
                          style={{ marginLeft: '6px' }}
                        />
                      </AppTooltip>
                    </Typography>
                    <Controller
                      name="media_transcript"
                      control={control}
                      defaultValue={''}
                      render={({ field }) => (
                        /* <TextField
                          {...field}
                          value={field.value ?? VIDEO_TRANSCRIPTION_GENERAL_ERROR_MSG}
                          multiline
                          rows={8}
                          fullWidth
                          disabled={true}
                          sx={{
                            height: '154px',
                            '& .MuiInputBase-root': {
                              backgroundColor: isDarkTheme ? '#212124' : '#ffffff',
                              borderRadius: '12px',
                              py: '12px',
                              '& .MuiInputBase-input': {
                                fontFamily: 'Graphik Regular',
                                fontSize: '14px',
                                color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000',
                                lineHeight: '16px',
                                wordBreak: 'none',
                              },
                              '& .MuiOutlinedInput-input.Mui-disabled': {
                                color:
                                  theme.palette.mode === 'dark'
                                    ? '#ffffff !important'
                                    : '#000000 !important',
                                WebkitTextFillColor:
                                  theme.palette.mode === 'dark'
                                    ? '#ffffff !important'
                                    : '#000000 !important',
                              },
                            },
                          }}
                        ></TextField>  */
                        <Box
                          className="text-sm"
                          sx={{
                            overflowY: 'scroll',
                            maxHeight: '152px',
                            maxWidth: '328px',
                            width: '100%',
                            backgroundColor: theme.palette.mode === 'dark' ? '#3D3E44' : '#eceeef',
                            padding: '12px',
                            borderRadius: '12px',
                            marginTop: '-2px',
                          }}
                        >
                          {(field.value && formatTranscript(field.value)) || TRANSCRIPT_NOT_READY_MSG}
                        </Box>
                      )}
                    />
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>
          </Box>
          {openImageCropper && (
            <ImageCropDialog
              open={openImageCropper}
              onSaved={(image) => {
                setOpenImageCropper(false)
                // Set the entire image object to the thumbnailFile state
                setThumbnailFile(image)
                // Update the thumbnail_url of the selected lesson in the selected section
                setSections((prevSections) => {
                  const updatedSections = { ...prevSections }
                  updatedSections[selectedSectionId].ContentLibraryCourseLesson[selectedLessonId].thumbnail_url =
                    image.croppedImg.url
                  return updatedSections
                })
                setHasUnsavedChanges(true)
                setValue('thumbnail_url', image.croppedImg.url)
              }}
              onClose={() => setOpenImageCropper(false)}
              setImage={() => {}}
              image={thumbnailFile} // Pass the file to the dialog
              aspectRatio={571 / 320}
              initialCroppedArea={{
                croppedArea: thumbnailFile?.croppedImg?.croppedArea,
                croppedAreaPixels: thumbnailFile?.croppedImg?.croppedAreaPixels,
                zoom: thumbnailFile?.croppedImg?.zoom || thumbnailFile?.croppedImg?.zoom,
              }}
              onCancel={() => {
                setThumbnailFile(previousThumbnailFile)
                fileInputRef.current.value = null
                setOpenImageCropper(false)
                setValue('thumbnail_url', previousThumbnailFile?.croppedImg?.url)
              }}
              placeholderHeight={thumbnailPlaceholderDimensions.height}
              placeholderWidth={thumbnailPlaceholderDimensions.width}
            />
          )}
        </>
      )}
    </Box>
  )
}

export default ShowVideoMedia
