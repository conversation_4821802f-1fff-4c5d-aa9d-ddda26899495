import Button from '@mui/material/Button'
import { styled, useTheme } from '@mui/system'
import React from 'react'

type StyledDivProps = {
  isLike?: boolean
  hoverClass?: string
}

const StyledDiv = styled('div')<StyledDivProps>(() => ({
  borderRadius: '50%',
  width: 39,
  height: 39,
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  transition: 'background-color 0.3s ease',
}))

const IconButtonWithBackground: React.FC<{
  icon: React.ReactNode
  onClick?: (e: any) => void
  disabled?: boolean
  isLike?: boolean
  styles?: React.CSSProperties
}> = ({ icon, onClick, disabled, isLike, styles = {} }) => {
  const theme = useTheme()

  const rgbaComponents = theme.palette.primary.main.match(/[\d.]+/g) || []
  const adjustedRgbaColor = `rgba(${rgbaComponents.slice(0, 3).join(', ')}, 0.3)`

  const hoverClass = isLike ? 'icon-hover-primary' : ''

  return (
    <>
      <style>
        {`
          .icon-hover-primary:hover {
            background-color: ${adjustedRgbaColor} !important;
            transition: background-color 0.3s ease;
          }
        `}
      </style>
      <Button
        variant="text"
        size="small"
        onClick={onClick}
        disabled={disabled}
        sx={{
          fontSize: 12,
          p: 0,
          '&:hover': {
            background: 'transparent',
          },
          '&& .MuiTouchRipple-child': {
            background: 'transparent',
          },
        }}
        startIcon={
          <StyledDiv className={hoverClass} style={styles}>
            {icon}
          </StyledDiv>
        }
      ></Button>
    </>
  )
}

export default IconButtonWithBackground
