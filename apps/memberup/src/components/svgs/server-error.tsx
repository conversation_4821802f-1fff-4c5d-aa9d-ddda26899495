import React from 'react'

const SVGServerError: React.FC<{ width?: number; height?: number; strokeWidth?: number }> = ({
  width,
  height,
  strokeWidth,
}) => {
  return (
    <svg
      width={width || 175}
      height={height || 105}
      viewBox="0 0 175 105"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-632.000000, -233.000000)">
          <g transform="translate(633.000000, 234.000000)">
            <path
              d="M13.5169826,84 L12,84 C5.372583,84 4.82947016e-14,78.627417 4.82947016e-14,72 L0,12 C0,5.372583 5.372583,0 12,0 L148,0 C154.627417,0 160,5.372583 160,12 L160,14.9496166"
              id="Path"
              stroke="currentColor"
              strokeWidth="2"
              strokeDasharray="3,3"
            ></path>
            <g transform="translate(14.000000, 14.000000)">
              <path
                d="M12,1 C5.92486775,1 1,5.92486775 1,12 L1,28 C1,34.0751322 5.92486775,39 12,39 L148,39 C154.075132,39 159,34.0751322 159,28 L159,12 C159,5.92486775 154.075132,1 148,1 L12,1 Z"
                stroke="currentColor"
                strokeWidth="2"
              ></path>
              <g transform="translate(8.000000, 8.000000)">
                <circle stroke="currentColor" strokeWidth="2" cx="12" cy="12" r="11"></circle>
                <g transform="translate(2.000000, 2.000000)">
                  <rect fill="none" x="0" y="0" width="20" height="20"></rect>
                  <g strokeWidth="1" transform="translate(4.000000, 4.000000)" fill="currentColor">
                    <path
                      d="M7.41425,6.00025 L11.70725,1.70725 C12.09825,1.31625 12.09825,0.68425 11.70725,0.29325 C11.31625,-0.09775 10.68425,-0.09775 10.29325,0.29325 L6.00025,4.58625 L1.70725,0.29325 C1.31625,-0.09775 0.68425,-0.09775 0.29325,0.29325 C-0.09775,0.68425 -0.09775,1.31625 0.29325,1.70725 L4.58625,6.00025 L0.29325,10.29325 C-0.09775,10.68425 -0.09775,11.31625 0.29325,11.70725 C0.48825,11.90225 0.74425,12.00025 1.00025,12.00025 C1.25625,12.00025 1.51225,11.90225 1.70725,11.70725 L6.00025,7.41425 L10.29325,11.70725 C10.48825,11.90225 10.74425,12.00025 11.00025,12.00025 C11.25625,12.00025 11.51225,11.90225 11.70725,11.70725 C12.09825,11.31625 12.09825,10.68425 11.70725,10.29325 L7.41425,6.00025 Z"
                      fillRule="nonzero"
                    ></path>
                  </g>
                </g>
              </g>
              <path
                d="M82.8028643,24.1486955 L83.6363636,24.7599283 L84.6365628,24.0264489 L82.8028643,24.1486955 Z M92.8028643,23.4820288 L94.5454545,24.7599283 L96.6365628,23.2264489 L92.8028643,23.4820288 Z M43.3634372,24.7735511 L47.1971357,24.5179712 L45.4545455,23.2400717 L43.3634372,24.7735511 Z M55.3634372,23.9735511 L57.1971357,23.8513045 L56.3636364,23.2400717 L55.3634372,23.9735511 Z"
                stroke="currentColor"
                strokeWidth="2"
              ></path>
              <path
                d="M132.278553,23.8357995 L133.497087,23.5379356 L133.090909,23.2400717 L132.278553,23.8357995 Z M138.139276,24.4620644 L138.545455,24.7599283 L139.357811,24.1642005 L138.139276,24.4620644 Z"
                stroke="currentColor"
                strokeWidth="2"
              ></path>
              <path
                d="M82.8028643,16.1486955 L83.6363636,16.7599283 L84.6365628,16.0264489 L82.8028643,16.1486955 Z M92.8028643,15.4820288 L94.5454545,16.7599283 L96.6365628,15.2264489 L92.8028643,15.4820288 Z M43.3634372,16.7735511 L47.1971357,16.5179712 L45.4545455,15.2400717 L43.3634372,16.7735511 Z M55.3634372,15.9735511 L57.1971357,15.8513045 L56.3636364,15.2400717 L55.3634372,15.9735511 Z"
                stroke="currentColor"
                strokeWidth="2"
              ></path>
              <path
                d="M132.278553,15.8357995 L133.497087,15.5379356 L133.090909,15.2400717 L132.278553,15.8357995 Z M138.139276,16.4620644 L138.545455,16.7599283 L139.357811,16.1642005 L138.139276,16.4620644 Z"
                stroke="currentColor"
                strokeWidth="2"
              ></path>
            </g>
            <g transform="translate(14.000000, 64.000000)">
              <path
                d="M12,1 C5.92486775,1 1,5.92486775 1,12 L1,28 C1,34.0751322 5.92486775,39 12,39 L148,39 C154.075132,39 159,34.0751322 159,28 L159,12 C159,5.92486775 154.075132,1 148,1 L12,1 Z"
                id="Rectangle"
                stroke="currentColor"
                strokeWidth="2"
              ></path>
              <g transform="translate(8.000000, 8.000000)">
                <circle fill="none" stroke="currentColor" strokeWidth="2" cx="12" cy="12" r="11"></circle>
                <g transform="translate(2.000000, 2.000000)">
                  <rect fill="none" x="0" y="0" width="20" height="20"></rect>
                  <g strokeWidth="1" transform="translate(4.000000, 4.000000)" fill="currentColor">
                    <path
                      d="M7.41425,6.00025 L11.70725,1.70725 C12.09825,1.31625 12.09825,0.68425 11.70725,0.29325 C11.31625,-0.09775 10.68425,-0.09775 10.29325,0.29325 L6.00025,4.58625 L1.70725,0.29325 C1.31625,-0.09775 0.68425,-0.09775 0.29325,0.29325 C-0.09775,0.68425 -0.09775,1.31625 0.29325,1.70725 L4.58625,6.00025 L0.29325,10.29325 C-0.09775,10.68425 -0.09775,11.31625 0.29325,11.70725 C0.48825,11.90225 0.74425,12.00025 1.00025,12.00025 C1.25625,12.00025 1.51225,11.90225 1.70725,11.70725 L6.00025,7.41425 L10.29325,11.70725 C10.48825,11.90225 10.74425,12.00025 11.00025,12.00025 C11.25625,12.00025 11.51225,11.90225 11.70725,11.70725 C12.09825,11.31625 12.09825,10.68425 11.70725,10.29325 L7.41425,6.00025 Z"
                      fillRule="nonzero"
                    ></path>
                  </g>
                </g>
              </g>
              <path
                d="M82.8028643,24.1486955 L83.6363636,24.7599283 L84.6365628,24.0264489 L82.8028643,24.1486955 Z M92.8028643,23.4820288 L94.5454545,24.7599283 L96.6365628,23.2264489 L92.8028643,23.4820288 Z M43.3634372,24.7735511 L47.1971357,24.5179712 L45.4545455,23.2400717 L43.3634372,24.7735511 Z M55.3634372,23.9735511 L57.1971357,23.8513045 L56.3636364,23.2400717 L55.3634372,23.9735511 Z"
                stroke="currentColor"
                strokeWidth="2"
                transform="translate(70.000000, 24.000000) scale(1, -1) translate(-70.000000, -24.000000) "
              ></path>
              <path
                d="M132.278553,23.8357995 L133.497087,23.5379356 L133.090909,23.2400717 L132.278553,23.8357995 Z M138.139276,24.4620644 L138.545455,24.7599283 L139.357811,24.1642005 L138.139276,24.4620644 Z"
                stroke="currentColor"
                strokeWidth="2"
                transform="translate(135.818182, 24.000000) scale(1, -1) translate(-135.818182, -24.000000) "
              ></path>
              <path
                d="M82.8028643,16.1486955 L83.6363636,16.7599283 L84.6365628,16.0264489 L82.8028643,16.1486955 Z M92.8028643,15.4820288 L94.5454545,16.7599283 L96.6365628,15.2264489 L92.8028643,15.4820288 Z M43.3634372,16.7735511 L47.1971357,16.5179712 L45.4545455,15.2400717 L43.3634372,16.7735511 Z M55.3634372,15.9735511 L57.1971357,15.8513045 L56.3636364,15.2400717 L55.3634372,15.9735511 Z"
                stroke="currentColor"
                strokeWidth="2"
                transform="translate(70.000000, 16.000000) scale(1, -1) translate(-70.000000, -16.000000) "
              ></path>
              <path
                d="M132.278553,15.8357995 L133.497087,15.5379356 L133.090909,15.2400717 L132.278553,15.8357995 Z M138.139276,16.4620644 L138.545455,16.7599283 L139.357811,16.1642005 L138.139276,16.4620644 Z"
                stroke="currentColor"
                strokeWidth="2"
                transform="translate(135.818182, 16.000000) scale(1, -1) translate(-135.818182, -16.000000) "
              ></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGServerError
