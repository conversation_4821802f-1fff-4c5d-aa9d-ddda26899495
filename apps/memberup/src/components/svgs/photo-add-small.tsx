import React from 'react'

const SVGPhotoAddSmall: React.FC<{ width?: number; height?: number; styles?: any }> = ({
  width = 16,
  height = 16,
  styles,
}) => {
  return (
    <svg width={width || '16px'} height={height || '16px'} style={{ ...styles }} viewBox="0 0 16 16">
      <g id="🔶-Symbols" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="Icons/system/20px/photo-add" transform="translate(-2, -2)" fill="currentColor">
          <path
            d="M9,2 C9.55228475,2 10,2.44771525 10,3 C10,3.55228475 9.55228475,4 9,4 L5,4 C4.44771525,4 4,4.44771525 4,5 L4,12.697 L6.18626653,9.41876181 C6.50017051,8.97929623 7.09949173,8.87371288 7.54124102,9.158983 L7.6401844,9.23177872 L12.7852568,13.5193391 L14.744,10 L17,10 C17.5522847,10 18,10.4477153 18,11 L18,15 C18,16.6568542 16.6568542,18 15,18 L5,18 C3.34314575,18 2,16.6568542 2,15 L2,5 C2,3.34314575 3.34314575,2 5,2 L9,2 Z M6.5,5 C7.32842712,5 8,5.67157288 8,6.5 C8,7.32842712 7.32842712,8 6.5,8 C5.67157288,8 5,7.32842712 5,6.5 C5,5.67157288 5.67157288,5 6.5,5 Z M15,2 C15.5522847,2 16,2.44771525 16,3 L16,4 L17,4 C17.5522847,4 18,4.44771525 18,5 C18,5.55228475 17.5522847,6 17,6 L16,6 L16,7 C16,7.55228475 15.5522847,8 15,8 C14.4477153,8 14,7.55228475 14,7 L14,6 L13,6 C12.4477153,6 12,5.55228475 12,5 C12,4.44771525 12.4477153,4 13,4 L14,4 L14,3 C14,2.44771525 14.4477153,2 15,2 Z"
            id="Shape"
          ></path>
        </g>
      </g>
    </svg>
  )
}

export default SVGPhotoAddSmall
