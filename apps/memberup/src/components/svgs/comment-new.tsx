import React from 'react'

const SVGCommentNew: React.FC<{ width?: number; height?: number; padding?: number }> = ({
  width,
  height,
  padding = 0,
}) => {
  return (
    <svg
      width={width || 16}
      height={height || 16}
      viewBox={`${padding} ${padding} ${16 - padding * 2} ${16 - padding * 2}`}
      version="1.1"
    >
      <g id="🔶-Symbols" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="Icons/feed/20px/comment" transform="translate(-1.999, -2)" fill="currentColor">
          <path
            d="M9.99902875,2 C14.4171926,2 17.9990288,5.58163076 17.9990288,9.99959852 L17.9917166,10.3429334 C17.9479209,11.3695994 17.7080893,12.3646756 17.2910254,13.2864807 L17.1830288,13.512 L17.947403,16.250408 L17.9753619,16.3692919 C18.1634952,17.3559157 17.243956,18.2268865 16.2488272,17.9486374 L13.5110288,17.184 L13.2836802,17.2924325 C12.260281,17.755095 11.1464823,17.999197 9.99902875,17.999197 C5.58086495,17.999197 1.99902875,14.4175663 1.99902875,9.99959852 C1.99902875,5.58163076 5.58086495,2 9.99902875,2 Z M9.99902875,4 C6.68540877,4 3.99902875,6.68622594 3.99902875,9.99959852 C3.99902875,13.3129711 6.68540877,15.999197 9.99902875,15.999197 C11.0320314,15.999197 12.0257824,15.7362574 12.9126126,15.2418996 L13.2719885,15.0415678 L15.7250288,15.726 L15.0397115,13.2749784 L15.2403143,12.9153719 C15.7355235,12.0276454 15.9990288,11.0329163 15.9990288,9.99959852 C15.9990288,6.68622594 13.3126487,4 9.99902875,4 Z"
            id="Shape"
          ></path>
        </g>
      </g>
    </svg>
  )
}

export default SVGCommentNew
