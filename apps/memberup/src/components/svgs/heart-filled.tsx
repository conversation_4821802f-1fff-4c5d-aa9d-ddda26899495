import React from 'react'

const SVGHeartFilled: React.FC<{ className?: string; width?: number; height?: number; padding?: number }> = ({
  className,
  width,
  height,
  padding = 0,
}) => {
  return (
    <svg
      className={className}
      width={width || 16}
      height={height || 14}
      viewBox={`${padding} ${padding} ${16 - padding * 2} ${14 - padding * 2}`}
      version="1.1"
    >
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-2, -3)" fill="currentColor">
          <path d="M10,17 L3.4776,10.8424196 C2.5248,9.9432652 2,8.7457154 2,7.47039232 C2,6.19506924 2.5248,4.99751944 3.4776,4.09757148 C5.2536,2.42068431 8.42809207,2.6266013 10,5.09901164 C11.594914,2.59725811 14.7464,2.42068431 16.5224,4.09757148 C17.4752,4.99751944 18,6.19506924 18,7.47039232 C18,8.7457154 17.4752,9.9432652 16.5224,10.8424196 L10,17 Z" />
        </g>
      </g>
    </svg>
  )
}

export default SVGHeartFilled
