import React from 'react'

const SVGBook: React.FC<{ width?: number; height?: number; strokeWidth?: number }> = ({
  width,
  height,
  strokeWidth,
}) => {
  return (
    <svg width={width || 16} height={height || 12} viewBox="0 0 16 12" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" strokeWidth={strokeWidth || 1} fill="none" fillRule="evenodd">
        <g transform="translate(-2.000000, -4.000000)" fill="currentColor" fillRule="nonzero">
          <g transform="translate(2.000000, 4.000000)">
            <path d="M6,0.373186613 L8,1.1483075 L10,0.373186613 C11.2838827,-0.124395537 12.7161173,-0.124395537 14,0.373186613 L15.4971125,0.953408223 C15.8008392,1.07112067 16,1.35613941 16,1.6730899 L16,10.8551209 C16,11.2832083 15.6418278,11.6302418 15.2,11.6302418 C15.0982245,11.6302418 14.9973837,11.6114256 14.9028875,11.5748026 L14,11.2248791 C12.7161173,10.727297 11.2838827,10.727297 10,11.2248791 L8,12 L6,11.2248791 C4.71611731,10.727297 3.28388269,10.727297 2,11.2248791 L1.09711254,11.5748026 C0.68688573,11.7337903 0.22130937,11.5404628 0.05721865,11.1429936 C0.01942016,11.0514361 0,10.9537313 0,10.8551209 L0,1.6730899 C0,1.35613941 0.19916076,1.07112067 0.50288746,0.953408223 L2,0.373186613 C3.28388269,-0.124395537 4.71611731,-0.124395537 6,0.373186613 Z M2.72274014,2.23803156 L2,2.51813705 L2,9.1213337 C3.55163935,8.6947532 5.21145561,8.7743201 6.72274014,9.3600342 L7,9.468 L7,3 L7.005,2.908 L5.27725986,2.23803156 C4.45835427,1.92065615 3.54164573,1.92065615 2.72274014,2.23803156 Z M10.7227401,2.23803156 L8.9957331,2.90700359 L9,3 L9,9.467 L9.2772599,9.3600342 C10.7885444,8.7743201 12.4483606,8.6947532 14,9.1213337 L14,2.51813705 L13.2772599,2.23803156 C12.4583543,1.92065615 11.5416457,1.92065615 10.7227401,2.23803156 Z"></path>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGBook
