import React from 'react'

const SVGBank: React.FC<{ width?: number; height?: number; strokeWidth?: number }> = ({
  width,
  height,
  strokeWidth,
}) => {
  return (
    <svg width={width || 20} height={height || 20} viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-296.000000, -216.000000)">
          <g transform="translate(272.000000, 104.000000)">
            <g transform="translate(24.000000, 112.000000)">
              <path
                d="M17.9222672,1.******** L11.7670926,0.********* C10.5966171,-0.********* 9.********,-0.********** 8.********,0.********* L2.********,1.******** C-1.********,2.******** -0.*********,7.******** 2.********,7.******** L17.158477,7.******** C20.4597041,7.******** 21.0742057,2.******** 17.9222672,1.******** Z M17.158477,5.******** L2.********,5.******** C1.3533453,5.******** 1.********,3.******** 2.********,3.******** L8.********,1.******** C9.********,1.50017974 10.4518998,1.49967193 11.346967,1.74939817 L17.5021416,3.46677881 C18.9214575,3.89935638 18.6430016,5.94212723 17.158477,5.******** Z M11.5607611,16.875004 C11.1291878,16.875004 10.7793501,16.5252388 10.7793501,16.093755 L10.7793501,9.80470045 C10.8206086,8.76813926 12.3012652,8.76892051 12.3421721,9.80470045 L12.3421721,16.093755 C12.3421721,16.5252388 11.9923344,16.875004 11.5607611,16.875004 Z M8.39604647,16.875004 C7.96447316,16.875004 7.61463545,16.5252388 7.61463545,16.093755 L7.61463545,9.80470045 C7.65589396,8.76813926 9.13655061,8.76892051 9.17745748,9.80470045 L9.17745748,16.093755 C9.17745748,16.5252388 8.82761977,16.875004 8.39604647,16.875004 Z M2.14475837,16.9140664 C1.71318507,16.9140664 1.36334736,16.5643012 1.36334736,16.1328174 L1.36334736,9.80470045 C1.40460586,8.76813926 2.88526251,8.76892051 2.92616938,9.80470045 L2.92616938,16.1328174 C2.92616938,16.5643012 2.57633167,16.9140664 2.14475837,16.9140664 Z M5.30947297,16.875004 C4.87789967,16.875004 4.52806196,16.5252388 4.52806196,16.093755 L4.52806196,9.80470045 C4.56932046,8.76813926 6.04997711,8.76892051 6.09088398,9.80470045 L6.09088398,16.093755 C6.09088398,16.5252388 5.74104627,16.875004 5.30947297,16.875004 Z M13.3580064,19.218751 C13.3580064,19.6502348 13.0081687,20 12.5765954,20 L0.777289097,20 C-0.259487034,19.9587501 -0.258705623,18.4784004 0.777289097,18.437502 L12.5765954,18.437502 C13.0081687,18.437502 13.3580064,18.7872672 13.3580064,19.218751 Z M20,16.0772315 C20,16.7570744 19.7240838,17.3973861 19.223004,17.8801199 C18.8689076,18.2212913 18.4358496,18.4517207 17.9683314,18.5534393 L17.9683314,19.218751 C17.9270729,20.2553122 16.4464162,20.2545309 16.4055093,19.218751 L16.4055093,18.4969941 C15.8844645,18.3971505 15.2734401,18.2309397 14.6712457,17.9916432 C14.2701865,17.8322293 14.0743259,17.3780111 14.2337337,16.9770351 C14.3931806,16.576059 14.8475321,16.3803952 15.2485131,16.5396138 C16.134516,16.8917618 17.0494702,17.0501209 17.4161863,17.0501209 C17.9696207,17.0501209 18.4371389,16.6046137 18.4371389,16.0772315 C18.4371389,15.5407869 17.887299,15.1043422 17.2114957,15.1043422 C16.4032042,15.1043422 15.6968868,14.8621159 15.1690046,14.4037962 C13.5684013,13.0417666 14.4274846,10.3155592 16.4445408,10.0588798 L16.4445408,9.29684953 C16.4857993,8.26028834 17.966456,8.26106959 18.0073628,9.29684953 L18.0073628,10.1516531 C18.3988888,10.2290358 18.8562487,10.355481 19.3845607,10.5609104 C20.3358895,10.9750115 19.7984741,12.354541 18.8181158,12.0171195 C17.8579571,11.6437997 17.2363837,11.5959482 16.8021145,11.5959482 C16.2864223,11.5959482 15.8975531,12.0181742 15.8975531,12.5780954 C15.8975531,13.1545399 16.4255916,13.5418051 17.2114957,13.5418051 C18.7490782,13.5418441 20,14.6792255 20,16.0772315 L20,16.0772315 Z M9.********,2.******** C9.********,2.******** 9.********,3.******** 9.********,3.******** C9.********,5.******** 10.9234422,5.1622454 10.9747028,3.******** C10.9747028,3.******** 10.5373861,2.******** 9.********,2.******** Z"
                fill="currentColor"
                fillRule="nonzero"
              ></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGBank
