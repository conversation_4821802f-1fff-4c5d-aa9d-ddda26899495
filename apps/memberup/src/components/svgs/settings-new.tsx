import React from 'react'

const SVGSettingsNew: React.FC<{
  width?: number
  height?: number
  styles?: any
}> = ({ width, height, styles }) => {
  return (
    <svg width={'16px' || width} height={height || '16px'} viewBox="0 0 16 16" style={{ ...styles }}>
      <g id="🔶-Symbols" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="Icons/system/20px/settings" transform="translate(-2, -2)" fill="currentColor">
          <path
            d="M10.7919996,2.00092422 C10.8513027,2.00374283 10.9205644,2.01156527 10.9959718,2.02798491 L11.1691234,2.0709722 C11.5138451,2.17103089 11.7021868,2.34004655 11.9810441,2.92312663 L12.1291587,3.25076976 L12.28706,3.62728981 L12.4510469,4.03344986 L12.8342445,3.8629265 L13.1827105,3.71501361 L13.4711669,3.60194116 C13.5147736,3.58580224 13.5562786,3.57096733 13.5958811,3.5573626 L13.812261,3.48990579 C13.9434204,3.45398849 14.0505182,3.43539146 14.1463103,3.42938934 L14.2162339,3.42717546 C14.4976486,3.42717546 14.7823454,3.52607084 15.0015134,3.69974808 L15.1053181,3.79270688 L16.1619775,4.84498187 L16.3130916,5.00911163 C16.5964715,5.34081562 16.6591943,5.60340217 16.4605023,6.23104106 L16.3500879,6.54822238 L16.2016591,6.92673117 L15.9558697,7.51329226 L16.5450925,7.73561264 L16.8796319,7.870729 C17.5349181,8.14722247 17.7557046,8.33000592 17.8846163,8.65251681 L17.9112009,8.72455836 L17.9588536,8.88382273 C17.9829651,8.97410651 17.9932152,9.03819244 17.9973696,9.10962391 L18,9.22647322 L18,10.7102642 C18,10.8338635 17.995654,10.9055048 17.9638419,11.0332555 C17.836437,11.5448856 17.6948316,11.726403 16.7372232,12.1393244 L16.5479134,12.219596 L15.9705456,12.4544834 L16.2370344,13.0458263 L16.3839639,13.3945126 C16.7068909,14.1972123 16.6994414,14.5065299 16.4201082,14.8678268 L16.3669046,14.9335394 L16.2431824,15.0711601 L15.1561987,16.1574584 C14.9530319,16.3628776 14.6789188,16.4879553 14.4000997,16.5194996 L14.2606046,16.5274162 C14.0898435,16.5274162 13.8748941,16.4815066 13.5705691,16.3792348 L13.4107494,16.3233537 L13.0414016,16.1822489 L12.4850498,15.9527177 L12.3291381,16.3680812 L12.1842498,16.7375293 C11.8671813,17.5189802 11.6828647,17.7571895 11.3314767,17.891486 L11.2582523,17.9172956 L11.1042759,17.9615453 C11.0169211,17.9840875 10.955037,17.9936644 10.8861575,17.9975443 L10.7735334,18 L9.20803951,17.9990822 C9.14891972,17.9962846 9.07989629,17.9885211 9.00473872,17.9722247 C8.54230903,17.8719557 8.34048791,17.7503648 8.01586169,17.0709974 L7.94427952,16.9166858 L7.78648371,16.5521461 L7.54800277,15.9666407 L7.16457312,16.1370989 L6.81596047,16.2849491 C6.76313337,16.3065897 6.71281074,16.326779 6.66479302,16.3455909 L6.40274923,16.4425236 C6.16517153,16.5241086 5.99620276,16.5614229 5.85273914,16.5704192 L5.7829332,16.5726313 C5.50494962,16.5726313 5.22403502,16.475809 4.99889174,16.30038 L4.89123948,16.2061992 L3.80291849,15.118129 C3.39492401,14.6856225 3.29445782,14.4540102 3.58402426,13.6275543 L3.64716243,13.4540961 L3.79474119,13.0795884 L4.04308719,12.4880009 L3.63393707,12.3344006 L3.26904701,12.191188 L2.96887388,12.0636169 C2.42832739,11.8207489 2.23182797,11.6414245 2.11384857,11.34381 L2.08740965,11.2715124 L2.04015145,11.1117137 C2.01662182,11.0224054 2.00661994,10.9590351 2.00256643,10.888372 L2,10.7727539 L2,9.28807402 C2,9.16345815 2.00443351,9.09115737 2.03688116,8.96227946 C2.14871696,8.51808192 2.27013671,8.32366543 2.93111748,8.00947964 L3.09347073,7.93468094 L3.45277435,7.78019944 L4.02991126,7.5462537 L3.85081255,7.15003964 L3.68794751,6.77316853 C3.25239962,5.72820362 3.29141542,5.42102774 3.72774105,4.9593824 L3.79688137,4.88813915 L4.84611219,3.84128886 C5.04757169,3.63871075 5.32037386,3.51306354 5.59928091,3.48129858 L5.73894114,3.4733181 C5.9387314,3.4733181 6.19675325,3.53525065 6.59064792,3.67774606 L6.76800502,3.74380226 L7.12860964,3.88660642 L7.5156445,4.04736774 L7.7444643,3.44429625 L7.88363303,3.10253482 C8.16862027,2.43385416 8.35864746,2.21401828 8.69907524,2.09454147 L8.77516404,2.07012821 L8.93506625,2.02946642 C9.01225184,2.01218792 9.06675378,2.00485123 9.12744462,2.00187994 L10.7919996,2.00092422 Z M10.0003148,8 C11.1028458,8 12,8.89682412 12,9.99942198 C12,11.0518711 11.1825803,11.9179399 10.1493259,11.9944989 L10.0003148,12 C8.89731159,12 8,11.1021804 8,9.99942198 C8,8.94675829 8.81762044,8.0819379 9.85124908,8.00549279 L10.0003148,8 Z M8.75061335,5.78743725 L8.64517604,5.83875412 L8.0080862,6.10386172 C7.80128343,6.18991706 7.57286694,6.20357545 7.35928479,6.14496546 L7.309,6.127 L6.39222372,5.74610522 L6.06995414,5.61802876 L5.943,5.572 L5.526,5.987 L5.67327184,6.32624225 L6.108,7.288 L6.13136845,7.34991059 C6.18425985,7.52507181 6.18806687,7.71150963 6.14237009,7.888684 L6.09880899,8.01950695 L5.83830179,8.65255762 C5.74962483,8.86804881 5.58902205,9.04463269 5.38569481,9.15352937 L4.24274761,9.61757318 L4,9.721 L3.999,10.329 L4.33686198,10.4619966 L5.331,10.835 L5.25883065,10.8036335 C5.48611734,10.8855656 5.67471857,11.0464555 5.79169333,11.2548276 L5.84405934,11.3628249 L6.1046044,11.9940206 C6.19062538,12.2024147 6.20322409,12.4324434 6.14273111,12.646963 L6.124,12.698 L5.655481,13.8128328 L5.562,14.048 L5.979,14.466 L6.352117,14.3095555 L7.34032347,13.8702783 C7.29420356,13.8845469 7.24889233,13.9022271 7.2047673,13.9233087 L7.47870226,13.8380387 C7.61972972,13.8156018 7.76463019,13.8235222 7.90342752,13.8620702 L8.01746537,13.9012891 L8.65830325,14.1658556 C8.83301736,14.2379854 8.98200193,14.3573746 9.09001693,14.5083546 L9.15925966,14.6199716 L9.62190964,15.7576578 L9.725,15.999 L10.325,16 L10.4567027,15.66524 L10.823,14.686 L10.8515767,14.624036 C10.9415983,14.4498669 11.0809375,14.3059206 11.2520874,14.2102833 L11.3584546,14.1588426 L11.9965666,13.8955127 C12.2009687,13.811162 12.4263244,13.7973918 12.6375339,13.8541491 L12.669,13.865 L13.7551627,14.3139489 L14.056,14.428 L14.474,14.011 L14.3294937,13.6772203 L13.894,12.721 L13.9169773,12.7793961 C13.8198586,12.5687316 13.7995775,12.3322775 13.8569548,12.1103954 L13.9007068,11.9793377 L14.1607597,11.3490308 C14.2489805,11.1352045 14.4080669,10.9597205 14.6094955,10.8508012 L14.652,10.83 L15.767157,10.3782868 L16,10.278 L16,9.67 L15.8390554,9.60684529 L14.7377554,9.19724313 C14.5109818,9.11472979 14.3230265,8.95355535 14.206639,8.74516969 L14.1545609,8.63718818 L13.8944701,8.0034419 C13.8091547,7.79555928 13.7968137,7.56630626 13.857092,7.35247059 L13.874,7.304 L14.3397026,6.19658178 L14.437,5.95 L14.019,5.533 L13.7944721,5.62679956 L12.6513762,6.13101248 C12.476564,6.18356636 12.2905806,6.18725602 12.1138216,6.14167682 L11.9833006,6.09824819 L11.3428792,5.83499557 C11.127341,5.74639612 10.9506877,5.58584753 10.8417146,5.38254588 L10.816,5.327 L10.4426814,4.4007673 L10.273,4 L9.676,3.999 L9.61438887,4.15379052 L9.164,5.339 L9.13729465,5.39726406 C9.04744506,5.56070337 8.91350193,5.69597638 8.75061335,5.78743725 Z"
            id="Shape"
          ></path>
        </g>
      </g>
    </svg>
  )
}

export default SVGSettingsNew
