import React from 'react'

const SVGFilm: React.FC<{
  width?: number
  height?: number
  strokeWidth?: number
  styles?: any
}> = ({ width, height, strokeWidth, styles }) => {
  return (
    <svg width={width || 20} height={height || 18} style={{ ...styles }} viewBox="0 0 20 22" version="1.1">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g>
          <path
            d="M2,16 C2,17.1045695 2.8954305,18 4,18 L16,18 C17.1045695,18 18,17.1045695 18,16 L18,2 C18,0.8954305 17.1045695,6.53355148e-12 16,6.53355148e-12 L4,6.53577192e-12 C2.8954305,6.53577192e-12 2,0.8954305 2,2 L2,16 Z"
            stroke="currentColor"
            strokeWidth={strokeWidth || 2}
            transform="translate(10.000000, 9.000000) rotate(-270.000000) translate(-10.000000, -9.000000) "
          ></path>
          <rect
            fill="currentColor"
            transform="translate(10.000000, 5.000000) rotate(-270.000000) translate(-10.000000, -5.000000) "
            x="9"
            y="-4"
            width="2"
            height="18"
          ></rect>
          <rect
            fill="currentColor"
            transform="translate(5.000000, 2.500000) rotate(-270.000000) translate(-5.000000, -2.500000) "
            x="2.5"
            y="1.5"
            width="5"
            height="2"
          ></rect>
          <rect
            fill="currentColor"
            transform="translate(5.000000, 15.500000) rotate(-270.000000) translate(-5.000000, -15.500000) "
            x="2.5"
            y="14.5"
            width="5"
            height="2"
          ></rect>
          <polygon
            fill="currentColor"
            transform="translate(10.000000, 9.000000) rotate(-270.000000) translate(-10.000000, -9.000000) "
            points="2 10 2 8 18 8 18 10"
          ></polygon>
          <polygon
            fill="currentColor"
            transform="translate(15.000000, 2.500000) rotate(-270.000000) translate(-15.000000, -2.500000) "
            points="12.5 3.5 12.5 1.5 17.5 1.5 17.5 3.5"
          ></polygon>
          <polygon
            fill="currentColor"
            transform="translate(15.000000, 15.500000) rotate(-270.000000) translate(-15.000000, -15.500000) "
            points="12.5 16.5 12.5 14.5 17.5 14.5 17.5 16.5"
          ></polygon>
          <rect
            fill="currentColor"
            transform="translate(10.000000, 13.000000) rotate(-270.000000) translate(-10.000000, -13.000000) "
            x="9"
            y="4"
            width="2"
            height="18"
          ></rect>
        </g>
      </g>
    </svg>
  )
}

export default SVGFilm
