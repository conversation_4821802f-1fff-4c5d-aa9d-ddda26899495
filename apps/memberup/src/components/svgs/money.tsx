import React from 'react'

const SVGMoney: React.FC<{ width?: number; height?: number; strokeWidth?: number }> = ({
  width,
  height,
  strokeWidth,
}) => {
  return (
    <svg width={width || 72} height={height || 48} viewBox="0 0 72 48" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" strokeWidth={strokeWidth || 1} fill="none" fillRule="evenodd">
        <g transform="translate(-804.000000, -321.000000)" fill="currentColor" fillRule="nonzero">
          <g transform="translate(620.000000, 321.000000)">
            <g transform="translate(184.000000, 0.000000)">
              <path d="M57,48 L15,48 C6.71984055,47.9900817 0.009918349,41.2801595 0,33 L0,15 C0.009918349,6.71984055 6.71984055,0.009918349 15,0 L57,0 C65.2801595,0.009918349 71.9900817,6.71984055 72,15 L72,33 C71.9900817,41.2801595 65.2801595,47.9900817 57,48 Z M15,6 C10.0294373,6 6,10.0294373 6,15 L6,33 C6,37.9705627 10.0294373,42 15,42 L57,42 C61.9705627,42 66,37.9705627 66,33 L66,15 C66,10.0294373 61.9705627,6 57,6 L15,6 Z M36,36 C29.372583,36 24,30.627417 24,24 C24,17.372583 29.372583,12 36,12 C42.627417,12 48,17.372583 48,24 C48,30.627417 42.627417,36 36,36 L36,36 Z M36,18 C32.6862915,18 30,20.6862915 30,24 C30,27.3137085 32.6862915,30 36,30 C39.3137085,30 42,27.3137085 42,24 C42,20.6862915 39.3137085,18 36,18 Z M15,12 C13.3431458,12 12,13.3431458 12,15 C12,16.6568542 13.3431458,18 15,18 C16.6568542,18 18,16.6568542 18,15 C18,13.3431458 16.6568542,12 15,12 Z M54,15 C54,16.6568542 55.3431458,18 57,18 C58.6568542,18 60,16.6568542 60,15 C60,13.3431458 58.6568542,12 57,12 C55.3431458,12 54,13.3431458 54,15 Z M15,30 C13.3431458,30 12,31.3431458 12,33 C12,34.6568542 13.3431458,36 15,36 C16.6568542,36 18,34.6568542 18,33 C18,31.3431458 16.6568542,30 15,30 Z M54,33 C54,34.6568542 55.3431458,36 57,36 C58.6568542,36 60,34.6568542 60,33 C60,31.3431458 58.6568542,30 57,30 C55.3431458,30 54,31.3431458 54,33 Z"></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGMoney
