import React from 'react'

const SVGSmallLock: React.FC<{
  width?: number
  height?: number
  styles?: any
}> = ({ width, height, styles }) => {
  return (
    <svg width={'12px' || width} height={'16px' || height} viewBox="0 0 12 16" style={{ ...styles }}>
      <g id="🔶-Symbols" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="Icons/system/20px/lock" transform="translate(-4, -2)" fill="currentColor">
          <path
            d="M10,2 C12.1421954,2 13.8910789,3.68396847 13.9951047,5.80035966 L14,6 L14.0009007,8.17102423 C15.1656226,8.58311485 16,9.69411778 16,11 L16,15 C16,16.6568542 14.6568542,18 13,18 L7,18 C5.34314575,18 4,16.6568542 4,15 L4,11 C4,9.69374794 4.8348501,8.58248558 6.00008893,8.17067428 L6,6 C6,3.790861 7.790861,2 10,2 Z M13,10 L7,10 C6.44771525,10 6,10.4477153 6,11 L6,15 C6,15.5522847 6.44771525,16 7,16 L13,16 C13.5522847,16 14,15.5522847 14,15 L14,11 C14,10.4477153 13.5522847,10 13,10 Z M10.1492623,4.00548574 L10,4 C8.9456382,4 8.08183488,4.81587779 8.00548574,5.85073766 L8,6 L8,8 L12,8 L12,6 C12,4.9456382 11.1841222,4.08183488 10.1492623,4.00548574 L10,4 L10.1492623,4.00548574 Z"
            id="Shape"
          ></path>
        </g>
      </g>
    </svg>
  )
}

export default SVGSmallLock
