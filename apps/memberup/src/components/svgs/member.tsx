import React from 'react'

const SVGMember: React.FC<{ width?: number; height?: number; strokeWidth?: number }> = ({
  width,
  height,
  strokeWidth,
}) => {
  return (
    <svg width={width || 15} height={height || 20} viewBox="0 0 15 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-685.000000, -216.000000)">
          <g transform="translate(272.000000, 104.000000)">
            <g transform="translate(413.000000, 112.000000)">
              <path
                d="M14.2385787,20 C13.8180457,20 13.4771574,19.6502344 13.4771574,19.21875 C13.4771574,16.1601953 11.0519924,13.671875 8.07106599,13.671875 L6.92893401,13.671875 C3.94800761,13.671875 1.52284264,16.1601953 1.52284264,19.21875 C1.52284264,19.6502344 1.18195431,20 0.76142132,20 C0.340888325,20 0,19.6502344 0,19.21875 C0,15.2986328 3.10831218,12.109375 6.92893401,12.109375 L8.07106599,12.109375 C11.8916878,12.109375 15,15.2986328 15,19.21875 C15,19.6502344 14.6591117,20 14.2385787,20 Z"
                fill="currentColor"
                fillRule="nonzero"
              ></path>
              <path
                d="M7.42385787,10.546875 C4.58988579,10.546875 2.28426396,8.18121094 2.28426396,5.2734375 C2.28426396,2.36566406 4.58988579,0 7.42385787,0 C10.2578299,0 12.5634518,2.36566406 12.5634518,5.2734375 C12.5634518,8.18121094 10.2578299,10.546875 7.42385787,10.546875 Z M7.42385787,1.5625 C5.42958122,1.5625 3.8071066,3.22722656 3.8071066,5.2734375 C3.8071066,7.31964844 5.42958122,8.984375 7.42385787,8.984375 C9.41813452,8.984375 11.0406091,7.31964844 11.0406091,5.2734375 C11.0406091,3.22722656 9.41813452,1.5625 7.42385787,1.5625 Z"
                fill="currentColor"
                fillRule="nonzero"
              ></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGMember
