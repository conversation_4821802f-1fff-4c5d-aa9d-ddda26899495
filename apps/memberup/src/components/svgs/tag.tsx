import React from 'react'

const SVGTag: React.FC<{
  width?: number
  height?: number
  fontSize?: number
  strokeWidth?: number
}> = ({ width, height, fontSize, strokeWidth }) => {
  return (
    <svg
      width={width || '16px'}
      height={height || '16px'}
      viewBox="0 0 16 16"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-2.000000, -2.000000)" fill="#000000" fillRule="nonzero">
          <g transform="translate(2.000000, 2.000000)">
            <path d="M6.83418581,0 C7.46529136,0 8.0705485,0.2507057 8.5168075,0.69696471 L15.3030353,7.48319253 C16.2323216,8.4124788 16.2323216,9.9191496 15.3030353,10.8484359 L10.8484359,15.3030353 C9.9191496,16.2323216 8.4124788,16.2323216 7.48319253,15.3030353 L0.69696471,8.5168075 C0.2507057,8.0705485 0,7.46529136 0,6.83418581 L0,2.37958638 C0,1.06537711 1.06537711,0 2.37958638,0 L6.83418581,0 Z M6.83418581,2 L2.37958638,2 C2.16994661,2 2,2.16994661 2,2.37958638 L2,6.83418581 C2,6.93485838 2.03999202,7.03140766 2.11117828,7.10259391 L8.8974061,13.8888217 C9.0456438,14.0370594 9.2859846,14.0370594 9.4342223,13.8888217 L13.8888217,9.4342223 C14.0370594,9.2859846 14.0370594,9.0456438 13.8888217,8.8974061 L7.10259391,2.11117828 C7.03140766,2.03999202 6.93485838,2 6.83418581,2 Z M5.5,4 C6.32842712,4 7,4.67157288 7,5.5 C7,6.32842712 6.32842712,7 5.5,7 C4.67157288,7 4,6.32842712 4,5.5 C4,4.67157288 4.67157288,4 5.5,4 Z"></path>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGTag
