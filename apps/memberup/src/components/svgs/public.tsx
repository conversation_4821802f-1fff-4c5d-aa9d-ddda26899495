import React from 'react'

const SVGPublicIcon: React.FC<{ width?: number; height?: number; styles?: any }> = ({ width, height, styles }) => {
  return (
    <svg width={'11px' || width} height={'11px' || height} style={{ ...styles }} viewBox="0 0 11 11">
      <g id="🟣-Home-Feed---About-Section---Spark-Feed" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="🌑-Home---Public---Members-View" transform="translate(-1099, -224)" fill="#FFFFFF" fillRule="nonzero">
          <g id="5340283_world_www_web_icon" transform="translate(1099, 224)">
            <path
              d="M5.5,0 C2.46243388,0 0,2.46243388 0,5.5 C0,8.53756612 2.46243388,11 5.5,11 C8.53756612,11 11,8.53756612 11,5.5 C11,4.04130931 10.4205374,2.64236278 9.3890873,1.6109127 C8.35763722,0.579462622 6.95869069,0 5.5,0 Z M8.80339506,7.33333333 C8.87405759,6.72711534 8.90925367,6.1140223 8.90864198,5.5 C8.90914477,4.88579421 8.87280653,4.27258637 8.8,3.66666667 C9.7132716,4.13086104 10.2666667,4.83418585 10.2666667,5.5 C10.2666667,6.16581415 9.69290123,6.90664962 8.80339506,7.33333333 Z M0.733333333,5.5 C0.733333333,4.81074169 1.30709877,4.11679454 2.19660494,3.66666667 C2.12594241,4.27288466 2.09074633,4.8859777 2.09135802,5.5 C2.09085523,6.11420579 2.12719347,6.72741363 2.2,7.33333333 C1.2867284,6.86913896 0.733333333,6.16581415 0.733333333,5.5 Z M3.66666667,1.46666667 C3.36863902,1.94098877 3.14219432,2.45681877 2.99466667,2.99744991 C2.45522262,3.14393813 1.94033339,3.36944369 1.46666667,3.66666667 C1.90839823,2.68960565 2.69057986,1.90742402 3.66666667,1.46666667 Z M1.46666667,7.45 C2.05366259,7.80876802 2.69547248,8.07213698 3.36746667,8.23 L3.40706667,8.23 C4.21153767,8.42829482 5.03738799,8.53014387 5.86666667,8.53333333 C6.13066667,8.53333333 6.39026667,8.53333333 6.64106667,8.50733333 C6.88407196,8.4905807 7.06727704,8.28299006 7.05026667,8.04366667 C7.0332563,7.80434328 6.82247196,7.62391403 6.57946667,7.64066667 C6.34773333,7.658 6.11013333,7.66666667 5.86666667,7.66666667 C5.1981908,7.67010182 4.53132399,7.60181011 3.87786667,7.463 C3.73651853,6.81796749 3.66571485,6.15988219 3.66666667,5.5 C3.66666667,2.692 4.82826667,0.733333333 5.86666667,0.733333333 C6.30666667,0.733333333 6.74666667,1.06266667 7.13826667,1.66066667 C7.21800532,1.7863257 7.35741209,1.86314527 7.50786667,1.86433333 C7.59173859,1.86541379 7.67417661,1.8428613 7.74546667,1.79933333 C7.84408574,1.73785709 7.91386319,1.64031065 7.93944414,1.5281596 C7.96502508,1.41600854 7.94431335,1.29844281 7.88186667,1.20133333 C7.87265514,1.18618865 7.86236256,1.17170774 7.85106667,1.158 C8.92542165,1.63866741 9.78446637,2.48931941 10.2666667,3.55 C9.65800608,3.1804052 8.99219291,2.91108954 8.29546667,2.75266667 L8.29546667,2.75266667 C7.5004254,2.56184314 6.68497744,2.46582119 5.86666667,2.46666667 C5.60266667,2.46666667 5.34306667,2.46666667 5.09226667,2.49266667 C4.93506981,2.50350372 4.79568437,2.59611044 4.72661549,2.73560277 C4.65754661,2.8750951 4.66928751,3.04028083 4.75741549,3.1689361 C4.84554347,3.29759138 4.99666981,3.37017039 5.15386667,3.35933333 C5.38853333,3.342 5.62613333,3.33333333 5.86666667,3.33333333 C6.53514253,3.32989818 7.20200935,3.39818989 7.85546667,3.537 C7.99681481,4.18203251 8.06761848,4.84011781 8.06666667,5.5 C8.06666667,8.308 6.90506667,10.2666667 5.86666667,10.2666667 C5.42666667,10.2666667 4.98666667,9.93733333 4.59506667,9.33933333 C4.5177186,9.19613658 4.36564675,9.10746988 4.20096719,9.10955052 C4.03628763,9.11163115 3.88657543,9.20411071 3.81298282,9.34921452 C3.73939021,9.49431833 3.75424004,9.66774921 3.85146667,9.79866667 C3.86067819,9.81381135 3.87097078,9.82829226 3.88226667,9.842 C2.80791168,9.36133259 1.94886696,8.51068059 1.46666667,7.45 Z M7.33333333,9.53333333 C7.63244054,9.05528404 7.85891791,8.53532803 8.00533333,7.99052823 C8.54410921,7.8478006 9.05896635,7.62635983 9.53333333,7.33333333 C9.09160177,8.31039435 8.30942014,9.09257598 7.33333333,9.53333333 Z"
              id="Shape"
            ></path>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGPublicIcon
