export default function SVGLocation({ width, height }: { width?: number; height?: number }) {
  return (
    <svg
      width={width || '10'}
      height={height || '12'}
      viewBox="0 0 9.9999704 11.99998"
      fill="none"
      style={{ verticalAlign: 'middle' }}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="m 5,0 c 2.75357,0 4.99997,2.37767 4.99997,5.32846 0,1.71515 -1.4866,3.87992 -4.68509,6.42412 L 5,11.99998 C 1.58408,9.34858 0,7.09894 0,5.32846 0,2.37767 2.24637,0 5,0 Z M 5,3 C 3.89543,3 3,3.89544 3,5 3,6.10457 3.89543,7 5,7 6.10457,7 6.99997,6.10457 6.99997,5 6.99997,3.89544 6.10457,3 5,3 Z"
        fill="currentColor"
      />
    </svg>
  )
}
