import React from 'react'

const SVGUploadPhoto: React.FC<{
  width?: number
  height?: number
  styles?: any
}> = ({ width, height, styles }) => {
  return (
    <svg width={width || '12px'} height={height || '12px'} style={{ ...styles }} viewBox="0 0 12 12">
      <g id="🔶-Symbols" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="Icons/system/16px/photo" transform="translate(-2, -2)" fill="currentColor">
          <path
            d="M11,2 C12.6568542,2 14,3.34314575 14,5 L14,11 C14,12.6568542 12.6568542,14 11,14 L5,14 C3.34314575,14 2,12.6568542 2,11 L2,5 C2,3.34314575 3.34314575,2 5,2 L11,2 Z M11,4 L5,4 C4.44771525,4 4,4.44771525 4,5 L4,10.697 L6,9 L9,11 L12,8 L12,5 C12,4.44771525 11.5522847,4 11,4 Z M6.5,8 C7.32842712,8 8,7.32842712 8,6.5 C8,5.67157288 7.32842712,5 6.5,5 C5.67157288,5 5,5.67157288 5,6.5 C5,7.32842712 5.67157288,8 6.5,8 Z"
            id="Shape"
          ></path>
        </g>
      </g>
    </svg>
  )
}

export default SVGUploadPhoto
