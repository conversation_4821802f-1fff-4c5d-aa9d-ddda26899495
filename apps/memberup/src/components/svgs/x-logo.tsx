import React from 'react'

const SVGXLogo: React.FC<{ width?: string; height?: string; styles?: any }> = ({ width, height, styles }) => {
  return (
    <svg style={{ ...styles }} width={width || '600pt'} height={height || '600pt'} viewBox="0 0 600 600">
      <g id="#000000ff">
        <path
          fill="currentColor"
          opacity="1.00"
          d=" M 0.00 0.00 L 178.66 0.00 C 230.33 73.55 281.92 147.17 333.55 220.74 C 398.30 147.20 462.91 73.53 527.67 0.00 L 580.60 0.00 C 506.06 84.72 431.65 169.54 357.16 254.30 C 437.98 369.54 518.82 484.77 599.67 600.00 L 421.01 600.00 C 366.51 522.49 312.21 444.84 257.69 367.35 C 189.59 444.91 121.48 522.46 53.36 600.00 L 0.44 600.00 C 78.39 511.30 156.32 422.59 234.25 333.87 C 156.20 222.57 78.12 111.29 0.00 0.04 L 0.00 0.00 M 72.02 39.07 C 196.74 213.82 321.52 388.53 446.22 563.30 C 473.33 563.37 500.45 563.33 527.57 563.32 C 402.75 388.57 278.11 213.68 153.17 39.01 C 126.13 39.14 99.07 39.03 72.02 39.07 Z"
        />
      </g>
    </svg>
  )
}

export default SVGXLogo
