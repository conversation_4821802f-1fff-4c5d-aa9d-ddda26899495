import React from 'react'

const SVGMemberManagement: React.FC<{ width?: number; height?: number; styles?: any }> = ({
  width,
  height,
  styles,
}) => {
  return (
    <svg width={'23px' || width} height={'24px' || height} viewBox="0 0 23 24" style={{ ...styles }}>
      <g id="👩-Members---Management" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g
          id="Members---List-Copy---Search-by:-Copy-2"
          transform="translate(-293, -64)"
          fill="currentColor"
          fillRule="nonzero"
        >
          <g id="Group-6" transform="translate(272, 44)">
            <g id="fi-rr-user-time" transform="translate(21, 20)">
              <path
                d="M2.00014872,23 C2.00534304,19.2209998 4.56259649,15.9225686 8.22110666,14.976 C8.56979552,14.8970443 8.84987524,14.6380617 8.95584249,14.2966084 C9.06180974,13.9551551 8.97756559,13.583106 8.73484398,13.3206084 C8.49212238,13.0581108 8.12779851,12.9450443 7.77910965,13.024 C3.19088037,14.1498554 -0.0263160407,18.275703 0.000162235153,23 C0.000162235153,23.5522847 0.447874459,24 1.00015548,24 C1.55243649,24 2.00014872,23.5522847 2.00014872,23 L2.00014872,23 Z M8.47410495,11 C5.43655936,11 2.97414213,8.53756612 2.97414213,5.5 C2.97414213,2.46243388 5.43655936,0 8.47410495,0 C11.5116505,0 13.9740678,2.46243388 13.9740678,5.5 C13.970761,8.53619535 11.5102798,10.9966932 8.47410495,11 Z M17.0000001,11 C17.5690622,11 18.0303784,11.4477153 18.0303784,12 L18.0303784,13.142 C18.7541963,13.3219627 19.4138731,13.6897824 19.9386393,14.206 L20.950471,13.642 C21.1875019,13.5096464 21.4690227,13.4741682 21.7330224,13.5433804 C21.9970221,13.6125925 22.2218446,13.7808174 22.357968,14.011 C22.6417846,14.4890572 22.4730288,15.0998775 21.9808494,15.376 L20.9576836,15.947 C21.0611688,16.2891815 21.1162827,16.6434198 21.1215138,17 C21.1172874,17.3564428 21.0632139,17.7106783 20.9607747,18.053 L21.9839406,18.624 C22.3030996,18.8026328 22.4998201,19.1329456 22.5000001,19.4905127 C22.5001789,19.8480798 22.3037899,20.1785784 21.9848103,20.3575127 C21.6658307,20.5364471 21.2727211,20.5366328 20.9535621,20.358 L19.9417304,19.794 C19.4161087,20.3106862 18.7553035,20.6785392 18.0303784,20.858 L18.0303784,22 C18.0303784,22.5522847 17.5690622,23 17.0000001,23 C16.4309376,23 15.9696214,22.5522847 15.9696214,22 L15.9696214,20.858 C15.2458034,20.6780373 14.5861266,20.3102176 14.0613605,19.794 L13.0495288,20.358 C12.5561518,20.6341424 11.9255326,20.4698309 11.6410014,19.991 C11.3564703,19.5121691 11.5257733,18.9001424 12.0191503,18.624 L13.0423161,18.053 C12.9388309,17.7108185 12.8837171,17.3565802 12.878486,17 C12.8827123,16.6435572 12.9367859,16.2893217 13.039225,15.947 L12.0160592,15.376 C11.6969002,15.1973672 11.5001796,14.8670544 11.5000001,14.5094873 C11.4998209,14.1519202 11.6962099,13.8214216 12.0151895,13.6424873 C12.3341691,13.4635529 12.7272787,13.4633672 13.0464376,13.642 L14.0582693,14.206 C14.5838911,13.6893138 15.2446962,13.3214608 15.9696214,13.142 L15.9696214,12 C15.9696214,11.4477153 16.4309376,11 17.0000001,11 Z M16.9999999,15 C15.8618752,15 14.9392429,15.8954305 14.9392429,17 C14.9392429,18.1045695 15.8618752,19 16.9999999,19 C18.1381245,19 19.0607568,18.1045695 19.0607568,17 C19.0607568,15.8954305 18.1381245,15 16.9999999,15 Z M8.47410495,2 C6.54112139,2 4.97412861,3.56700338 4.97412861,5.5 C4.97412861,7.43299662 6.54112139,9 8.47410495,9 C10.4070885,9 11.9740813,7.43299662 11.9740813,5.5 C11.9740813,3.56700338 10.4070885,2 8.47410495,2 L8.47410495,2 Z"
                id="Shape"
              ></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGMemberManagement
