import useTheme from '@mui/material/styles/useTheme'
import React from 'react'

const SVGVerified: React.FC<{
  className?: string
  width?: number
  height?: number
  strokeWidth?: number
  styles?: any
  scale?: number
}> = ({ className, width, height, strokeWidth, styles = {}, scale }) => {
  const theme = useTheme()

  return (
    <svg
      className={className}
      width={width || 20}
      height={height || 20}
      viewBox="0 0 20 20"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      style={{
        ...styles,
        transform: `${styles.transform ? styles.transform + ' ' : ''}scale(${scale ? scale : 1})`,
      }}
    >
      <g stroke="none" strokeWidth={strokeWidth || 1} fill="none" fillRule="evenodd">
        <g transform="translate(-541.000000, -1506.000000)">
          <g transform="translate(369.000000, 1480.000000)">
            <g transform="translate(24.000000, 24.000000)">
              <g transform="translate(148.000000, 2.000000)">
                <path
                  d="M5.17500049,16.6485793 C4.32731585,16.8080299 3.51087139,16.250106 3.35142074,15.4024214 C3.31553082,15.2116205 3.31553082,15.0158004 3.35142074,14.8249995 C3.60812339,13.4602958 3.03346208,12.0729407 1.88695464,11.2894656 C1.17480245,10.8028107 0.992000795,9.83098551 1.47865571,9.11883332 C1.58819432,8.95853871 1.72666003,8.820073 1.88695464,8.71053439 C3.03346208,7.92705933 3.60812339,6.5397042 3.35142074,5.17500049 C3.19197009,4.32731585 3.74989397,3.51087139 4.5975786,3.35142074 C4.7883795,3.31553082 4.98419959,3.31553082 5.17500049,3.35142074 C6.5397042,3.60812339 7.92705933,3.03346208 8.71053439,1.88695464 C9.19718931,1.17480245 10.1690145,0.992000795 10.8811667,1.47865571 C11.0414613,1.58819432 11.179927,1.72666003 11.2894656,1.88695464 C12.0729407,3.03346208 13.4602958,3.60812339 14.8249995,3.35142074 C15.6726841,3.19197009 16.4891286,3.74989397 16.6485793,4.5975786 C16.6844692,4.7883795 16.6844692,4.98419959 16.6485793,5.17500049 C16.3918766,6.5397042 16.9665379,7.92705933 18.1130454,8.71053439 C18.8251975,9.19718931 19.0079992,10.1690145 18.5213443,10.8811667 C18.4118057,11.0414613 18.27334,11.179927 18.1130454,11.2894656 C16.9665379,12.0729407 16.3918766,13.4602958 16.6485793,14.8249995 C16.8080299,15.6726841 16.250106,16.4891286 15.4024214,16.6485793 C15.2116205,16.6844692 15.0158004,16.6844692 14.8249995,16.6485793 C13.4602958,16.3918766 12.0729407,16.9665379 11.2894656,18.1130454 C10.8028107,18.8251975 9.83098551,19.0079992 9.11883332,18.5213443 C8.95853871,18.4118057 8.820073,18.27334 8.71053439,18.1130454 C7.92705933,16.9665379 6.5397042,16.3918766 5.17500049,16.6485793 Z"
                  id="Star"
                  fill={theme.palette.primary.dark}
                ></path>
                <polyline
                  stroke="#FFFFFF"
                  strokeWidth={2}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  points="7 10.258904 9.01943515 12 13 8"
                ></polyline>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGVerified
