import { Tab, Tabs, Typography } from '@mui/material'
import { useRouter } from 'next/router'

import useAppTheme from '../hooks/use-app-theme'
import useCheckUserRole from '@/memberup/components/hooks/use-check-user-role'
import { selectMembershipSetting } from '@/memberup/store/features/membershipSlice'
import { openDialog } from '@/memberup/store/features/uiSlice'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'

const HeaderTabs = () => {
  const { theme, isDarkTheme, newGradient } = useAppTheme()
  const dispatch = useAppDispatch()
  const router = useRouter()
  const { isCurrentUserAdmin } = useCheckUserRole()
  const membershipSetting = useAppSelector((state) => selectMembershipSetting(state))
  const isAccountSettingsRoute = router.pathname.startsWith('/settings/account')

  let menuItems: object[]

  if (!isAccountSettingsRoute) {
    menuItems = [
      {
        name: 'Community',
        pathname: '/community',
      },
      {
        name: 'Content',
        pathname: '/library',
      },
      {
        name: 'Events',
        pathname: '/events',
      },
      {
        name: 'Members',
        pathname: '/members',
      },
    ]
  } else {
    menuItems = [
      {
        name: 'Profile',
        pathname: '/settings/account/profile',
      },
      {
        name: 'Account',
        pathname: '/settings/account/account',
      },
      {
        name: 'Password',
        pathname: '/settings/account/password',
      },
      // {
      //   name: 'Referrals',
      //   pathname: '/settings/account/referrals',
      // },
      {
        name: 'Notifications',
        pathname: '/settings/account/notifications',
      },
    ]

    if (
      !isCurrentUserAdmin &&
      membershipSetting?.stripe_connect_account &&
      membershipSetting?.stripe_connect_account?.enabled !== false
    ) {
      menuItems.push(
        {
          name: 'Payment Methods',
          pathname: '/settings/account/payment-methods',
        },
        {
          name: 'Payment History',
          pathname: '/settings/account/payment-history',
        },
      )
    }
  }

  if (!isAccountSettingsRoute && !isCurrentUserAdmin) {
    menuItems.push({
      name: 'Settings',
      action: () => {
        dispatch(openDialog({ dialog: 'MemberSettings', open: true, props: {} }))
      },
      key: 'settings',
    })
  }

  const tabStyles = {
    backgroundColor: isDarkTheme ? '#25262b' : '#edeef0',
    p: '0px 16px',
    borderRadius: '10px',
    m: '0px 6px 0px 0px',
    height: '40px',
    lineHeight: '40px',
    color: theme.palette.text.primary,
  }
  const selectedTabStyles = {
    ...tabStyles,
    color: theme.palette.text.primary,
    background: newGradient,
  }
  const currentPath = router.pathname
  return (
    <Tabs
      value={isAccountSettingsRoute ? 'Profile' : 'Content'}
      onChange={() => console.log('change')}
      sx={{
        '& .MuiTabs-indicator': {
          display: 'none',
        },
        '& .Mui-selected': {
          color: 'inherit !important',
        },
      }}
      variant="scrollable"
      scrollButtons={false}
      aria-label="scrollable prevent tabs example"
    >
      {menuItems.map((item: any) => {
        const selected =
          item.pathname === currentPath || (item.pathname === '/library' && currentPath.startsWith('/course/'))
        return (
          <Tab
            key={item.pathname !== undefined ? item.pathname : item.key}
            sx={selected ? selectedTabStyles : tabStyles}
            onClick={() => {
              if (item.action) {
                item.action()
              } else {
                router.push(item.pathname)
              }
            }}
            value={item.name}
            label={
              <>
                <Typography
                  sx={{
                    display: 'flex',
                    alignContent: 'center',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontFamily: 'Graphik Medium',
                    fontSize: '14px',
                    color: 'inherit',
                  }}
                >
                  {item.name}
                </Typography>
              </>
            }
          />
        )
      })}
    </Tabs>
  )
}

export default HeaderTabs
