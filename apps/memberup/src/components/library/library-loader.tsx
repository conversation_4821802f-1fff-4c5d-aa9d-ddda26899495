import { Box, Skeleton, Stack } from '@mui/material'
import useTheme from '@mui/material/styles/useTheme'

interface RotatedBoxProps {
  width?: number | string
  isPreview?: boolean
  height?: number | string
}

const LibrarySkeleton: React.FC<RotatedBoxProps> = ({ width, isPreview, height = '413px' }) => {
  const theme = useTheme()
  const circlesSize = isPreview ? 20 : 32
  const linesHeight = isPreview ? 8 : 14
  return (
    <Box
      sx={{
        backgroundColor: theme.palette.mode === 'dark' ? '#212124' : '#ffffff',
        width: width || 305,
        height: height,
        borderRadius: '10px',
        display: 'flex',
        flexDirection: 'column',
        paddingBottom: '10px',
      }}
    >
      <Box
        sx={{
          backgroundColor: theme.palette.mode === 'dark' ? '#1D1E20' : '#f1f2f4',
          height: '50%',
          borderRadius: '8px',
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
          padding: '10px',
        }}
      >
        <Skeleton
          sx={{ borderRadius: '50%', backgroundColor: 'rgba(141,148,163,0.08)' }}
          variant="rounded"
          width={circlesSize}
          height={circlesSize}
          animation={false}
        />

        <Skeleton
          sx={{ borderRadius: '50%', backgroundColor: 'rgba(141,148,163,0.08)' }}
          variant="rounded"
          width={circlesSize}
          height={circlesSize}
          animation={false}
        />
      </Box>
      <Box />
      <Stack
        sx={{ padding: '10px 10px 5px 10px', height: '50%' }}
        direction="column"
        spacing={2}
        justifyContent={'flex-end'}
      >
        <Stack spacing={'12px'}>
          <Skeleton
            sx={{ borderRadius: '10px', backgroundColor: 'rgba(141,148,163,0.08)' }}
            variant="rounded"
            height={linesHeight}
            width={'30%'}
            animation={false}
          />
          <Skeleton
            sx={{ borderRadius: '10px', backgroundColor: 'rgba(141,148,163,0.08)' }}
            variant="rounded"
            width={'90%'}
            height={linesHeight}
            animation={false}
          />
          <Skeleton
            sx={{ borderRadius: '10px', backgroundColor: 'rgba(141,148,163,0.08)' }}
            variant="rounded"
            width={'75%'}
            height={linesHeight}
            animation={false}
          />
        </Stack>
        <Stack sx={{ py: '6px' }} direction="row" spacing={2}>
          <Skeleton
            sx={{ borderRadius: '10px', backgroundColor: 'rgba(141,148,163,0.08)' }}
            variant="rounded"
            width={'33%'}
            height={linesHeight * 2}
            animation={false}
          />
          <Skeleton
            sx={{ borderRadius: '10px', backgroundColor: 'rgba(141,148,163,0.08)' }}
            variant="rounded"
            width={'33%'}
            height={linesHeight * 2}
            animation={false}
          />
          <Skeleton
            sx={{ borderRadius: '10px', backgroundColor: 'rgba(141,148,163,0.08)' }}
            variant="rounded"
            width={'33%'}
            height={linesHeight * 2}
            animation={false}
          />
        </Stack>
        <Skeleton
          sx={{ borderRadius: '10px', backgroundColor: 'rgba(141,148,163,0.08)' }}
          variant="rounded"
          width={'100%'}
          height={isPreview ? 22 : 40}
          animation={false}
        />
      </Stack>
    </Box>
  )
}

export default LibrarySkeleton
