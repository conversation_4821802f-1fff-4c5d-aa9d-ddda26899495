import React, { useState } from 'react'

import MemberRequestCard from '@/components/community/members/member-request-card'
import { ConfirmModal } from '@/components/ui/confirm-modal'
import { toast } from '@/components/ui/sonner'
import {
  approveMemberRequestApi,
  getMemberRequestsApi,
  rejectMemberRequestApi,
} from '@/shared-services/apis/membership.api'
import { selectMembership } from '@/src/store/features/membershipSlice'
import { useAppSelector } from '@/src/store/hooks'

const APPROVE_ACTION = 'approve'
const DECLINE_ACTION = 'decline'

export default function MembersRequests({ membersRequests, setMembersRequests, onMemberRequestsChanged }) {
  const membership = useAppSelector((state) => selectMembership(state))
  const [openConfirm, setOpenConfirm] = useState(false)
  const [selectedMemberRequest, setSelectedMemberRequest] = useState(null)
  const [action, setAction] = useState(null)
  const [loading, setLoading] = useState(false)

  const handleApproveClick = (mr) => {
    setOpenConfirm(true)
    setSelectedMemberRequest(mr)
    setAction(APPROVE_ACTION)
  }

  const handleDeclineClick = (mr) => {
    setOpenConfirm(true)
    setSelectedMemberRequest(mr)
    setAction(DECLINE_ACTION)
  }
  const handleConfirmProceed = async () => {
    setLoading(true)
    if (action === APPROVE_ACTION) {
      await approveMemberRequestApi(selectedMemberRequest.id)
      toast.success('The member request has been approved successfully')
    } else {
      await rejectMemberRequestApi(selectedMemberRequest.id)
      toast.success('The member request has been rejected successfully')
    }
    const res = await getMemberRequestsApi(membership.id)
    setMembersRequests(res.data.data)

    setLoading(false)
    setOpenConfirm(false)
  }

  const handleConfirmClose = () => {
    setOpenConfirm(false)
    setSelectedMemberRequest(null)
    setAction(null)
  }

  console.log('membersRequests', membersRequests)

  return (
    <>
      <div className={'flex'}></div>

      {loading && <>Loading...</>}

      {membersRequests.map((mr) => (
        <MemberRequestCard
          key={mr.id}
          memberRequest={mr}
          onApproveClick={handleApproveClick}
          onDeclineClick={handleDeclineClick}
        />
      ))}

      {membersRequests.length === 0 && (
        <div className="inline-flex h-[214px] w-full flex-col items-center justify-center gap-2.5 rounded-lg bg-black-500 p-4">
          <div className="font-['Graphik'] text-[14px] font-normal leading-loose text-black-100">
            You have 0 requests.
          </div>
        </div>
      )}
      <ConfirmModal
        onConfirm={handleConfirmProceed}
        open={openConfirm}
        onCancel={handleConfirmClose}
        title={'Please confirm'}
        loading={loading}
      >
        <>
          <div>{`Are you sure you want to ${action} the member request?`}</div>
        </>
      </ConfirmModal>
    </>
  )
}
