import _cloneDeep from 'lodash/cloneDeep'
import _get from 'lodash/get'
import _orderBy from 'lodash/orderBy'
import _set from 'lodash/set'
import _unset from 'lodash/unset'
import { all, call, put, select, takeEvery, takeLatest } from 'redux-saga/effects'

import { getContentLibrary, getContentLibraryFailure, getContentLibrarySuccess } from './features/contentLibrarySlice'
import {
  deleteEvent,
  deleteEventFailure,
  deleteEventSuccess,
  getEvents,
  getEventsFailure,
  getEventsSuccess,
  upsertEvent,
  upsertEventFailure,
  upsertEventSuccess,
} from './features/eventSlice'
import {
  deleteFeed,
  deleteFeedFailure,
  deleteFeedSuccess,
  reportFeed,
  upsertFeed,
  upsertFeedFailure,
  upsertFeedSuccess,
} from './features/feedSlice'
import {
  createLive,
  createLiveFailure,
  createLiveSuccess,
  getLive,
  getLiveFailure,
  getLiveSuccess,
  updateLive,
  updateLiveFailure,
  updateLiveSuccess,
} from './features/liveSlice'
import {
  disconnectStripeAccount,
  disconnectStripeAccountFailure,
  disconnectStripeAccountSuccess,
  getMembership,
  getMembershipFailure,
  getMembershipSuccess,
  selectMembership,
  selectMembershipSetting,
  updateMembershipSetting,
  updateMembershipSettingFailure,
  updateMembershipSettingSuccess,
} from './features/membershipSlice'
import {
  getMembers,
  getMembersSuccess,
  getMemberSuccess,
  setActiveMember,
  setMemberError,
  updateMemberProfile,
} from './features/memberSlice'
import {
  deleteChannel,
  deleteChannelFailure,
  deleteChannelSuccess,
  getChannels,
  getChannelsSuccess,
  resetSpaceState,
  selectChannels,
  setSpaceError,
  upsertChannel,
  upsertChannelFailure,
  upsertChannelSuccess,
} from './features/spaceSlice'
import {
  getActiveUser,
  getActiveUserFailure,
  getActiveUserSuccess,
  selectUser,
  startMembership,
  updateUserProfile,
  updateUserProfileFailure,
  updateUserProfileSuccess,
} from './features/userSlice'
import { uploadFileToCloudinaryApi } from '@memberup/shared/src/services/apis/cloudinary.api'
import { getContentLibraryApi } from '@memberup/shared/src/services/apis/content-library.api'
import { createLiveApi, getLiveApi, updateLiveApi } from '@memberup/shared/src/services/apis/live.api'
import { updateMembershipSettingApi } from '@memberup/shared/src/services/apis/membership-settings.api'
import { getMembershipApi } from '@memberup/shared/src/services/apis/membership.api'
import { disconnectStripeAccountApi } from '@memberup/shared/src/services/apis/stripe.api'
import {
  getActiveUserApi,
  getUserApi,
  getUsersApi,
  startMembershipApi,
  updateUserProfileApi,
} from '@memberup/shared/src/services/apis/user.api'
import { IMembershipSetting } from '@memberup/shared/src/types/interfaces'
import { toast } from '@/components/ui/sonner'
import {
  createChannelApi,
  deleteChannelApi,
  getChannelsApi,
  updateChannelApi,
} from '@/shared-services/apis/channel.api'
import { createEventApi, deleteEventApi, getEventsApi, updateEventApi } from '@/shared-services/apis/event.api'
import { createFeedApi, deleteFeedApi, reportFeedApi, updateFeedApi } from '@/shared-services/apis/feed.api'

function* getMembershipSaga(action) {
  try {
    const result = yield call(getMembershipApi, action.payload)

    if (result?.data?.success) {
      yield put(getMembershipSuccess(result.data.data))
      yield put(getContentLibrarySuccess(result.data.data.content_library))
    } else {
      yield put(
        getMembershipSuccess({
          membership: null,
          membership_setting: null,
          owner: null,
        }),
      )
    }
  } catch (err: any) {
    yield put(
      getMembershipFailure({
        status: err.response?.status,
        message: err.response?.data?.message,
      }),
    )
  }
}

function* resetSpaceSaga() {
  yield put(resetSpaceState())
}

function* updateMembershipSettingSaga(action) {
  const before = yield select(selectMembershipSetting)
  const membership = yield select(selectMembership)
  try {
    const payload = _cloneDeep(action.payload.data)
    const fileFields = [
      { fileField: 'logo_file', field: 'logo' },
      { fileField: 'favicon_file', field: 'favicon' },
      { fileField: 'cover_image_file', field: 'cover_image' },
      { fileField: 'library.cover_image_file', field: 'library.cover_image' },
      { fileField: 'signin.cover_image_file', field: 'signin.cover_image' },
      { fileField: 'signup_old.cover_image_file', field: 'signup_old.cover_image' },
      { fileField: 'signup_old.testimonial_image_file', field: 'signup_old.testimonial_image' },
      { fileField: 'signup_payment.cover_image_file', field: 'signup_payment.cover_image' },
      {
        fileField: 'signup_payment.testimonial_image_file',
        field: 'signup_payment.testimonial_image',
      },
    ]
    for (const item of fileFields) {
      const imageFile = _get(payload, item.fileField)
      if (imageFile) {
        const result = yield call(uploadFileToCloudinaryApi, imageFile)
        _set(payload, item.field, result.data.secure_url)
      }
      _unset(payload, item.fileField)
    }
    const result = yield call(updateMembershipSettingApi, before.id, payload, membership.id)
    if (result?.data?.success) {
      if (!action.payload.noToast) {
        toast.success('Changes saved')
      }

      yield put(updateMembershipSettingSuccess({ data: result.data.data as Partial<IMembershipSetting> }))
    } else {
      toast.error('Failed to save changes')
      yield put(
        updateMembershipSettingFailure({
          status: 400,
          message: 'Failed update membership settings.',
          data: before,
        }),
      )
    }
  } catch (err: any) {
    if (err.response?.data?.message) {
      toast.error(err.response.data.message)
    }
    yield put(
      updateMembershipSettingFailure({
        status: err.response?.status,
        message: err.response?.data?.message,
        data: before,
      }),
    )
  }
}

function* getMembersSaga(action) {
  try {
    const result = yield call(getUsersApi, action.payload)
    if (result?.data?.success) {
      yield put(getMembersSuccess(result.data.data))
    }
  } catch (err: any) {
    yield put(
      setMemberError({
        status: err.response?.status,
        message: err.response?.data?.message,
      }),
    )
  }
}

function* getMemberSaga(action) {
  try {
    const result = yield call(getUserApi, action.payload.id)
    if (result?.data?.success) {
      yield put(getMemberSuccess(result.data.data))
    }
  } catch (err: any) {
    yield put(
      setMemberError({
        status: err.response?.status,
        message: err.response?.data?.message,
      }),
    )
  }
}

function* getContentLibrarySaga(action) {
  try {
    const contentLibrary = yield call(getContentLibraryApi as any, action.payload)
    yield put(getContentLibrarySuccess(contentLibrary.data.data.content_library))
  } catch (err: any) {
    yield put(
      getContentLibraryFailure({
        status: err.response?.status,
        message: err.response?.data?.message,
      }),
    )
  }
}

function* disconnectStripeAccountSaga(action) {
  try {
    const result = yield call(disconnectStripeAccountApi, action.payload.isMembership)
    yield put(disconnectStripeAccountSuccess(result.data.data))
  } catch (err: any) {
    yield put(
      disconnectStripeAccountFailure({
        status: err.response?.status,
        message: err.response?.data?.message,
      }),
    )
  }
}

function* getActiveUserSaga(action) {
  try {
    const result = yield call(getActiveUserApi, action.payload)
    if (result?.data?.success) {
      if (result.data.data?.time_zone) {
        yield put(
          updateMembershipSettingSuccess({
            data: {
              time_zone: result.data.data.time_zone,
            },
            partialChanged: true,
          }),
        )
      }
      yield put(getActiveUserSuccess(result.data.data))
    } else {
      yield put(
        getActiveUserFailure({
          status: 400,
          message: 'The system has a problem to get the user.',
        }),
      )
    }
  } catch (err: any) {
    yield put(
      getActiveUserFailure({
        status: err.response?.status,
        message: err.response?.data?.message,
      }),
    )
  }
}

function* startMembershipSaga(action) {
  try {
    const result = yield call(startMembershipApi, action.payload)
    if (result?.data?.success) {
      yield put(getActiveUserSuccess(result.data.data))
    } else {
      yield put(
        getActiveUserFailure({
          status: 400,
          message: 'The system has a problem to get the user.',
        }),
      )
    }
  } catch (err: any) {
    yield put(
      getActiveUserFailure({
        status: err.response?.status,
        message: err.response?.data?.message,
      }),
    )
  }
}

function* updateUserProfileSaga(action) {
  try {
    const { image_file, cover_image_file, ...rest } = action.payload.data
    if (typeof image_file?.name === 'string') {
      const result = yield call(uploadFileToCloudinaryApi, image_file)
      rest['image'] = result.data.secure_url as string
    }

    if (typeof cover_image_file?.name === 'string') {
      const result = yield call(uploadFileToCloudinaryApi, cover_image_file)
      rest['cover_image'] = result.data.secure_url as string
    }

    const user = yield select(selectUser)
    const result = yield call(updateUserProfileApi, user.id, rest)

    if (result?.data?.success) {
      toast.success('Your profile has been updated')
      yield put(updateUserProfileSuccess(result.data.data))
      yield put(updateMemberProfile(result.data.data))
    } else {
      toast.error('Failed to update settings')
    }
  } catch (err: any) {
    yield put(
      updateUserProfileFailure({
        status: err.response?.status,
        message: err.response?.data?.message,
      }),
    )
  }
}

function* getChannelsSaga(action) {
  try {
    const result = yield call(getChannelsApi, action.payload)
    if (result?.data?.success) {
      yield put(
        getChannelsSuccess({
          data: {
            docs: _orderBy(result.data.data?.docs || [], ['sequence'], ['asc']),
            total: result.data.data?.total || 0,
          },
          init: action.payload.init,
        }),
      )
    } else {
      yield put(getChannelsSuccess({ data: { total: 0, docs: [] }, init: action.payload.init }))
    }
  } catch (err: any) {
    yield put(
      setSpaceError({
        status: err.response?.status,
        message: err.response?.data?.message,
      }),
    )
  }
}

function* deleteChannelSaga(action) {
  const before = yield select(selectChannels)
  const index = before.docs.findIndex((c) => c.id === action.payload)
  const channel = before.docs[index]

  try {
    const result = yield call(deleteChannelApi, action.payload)
    if (result?.data?.success) {
      yield put(deleteChannelSuccess(action.payload))
      toast.success('The space has been deleted successfully')
    } else {
      yield put(
        deleteChannelFailure({
          error: {
            status: 500,
            message: result?.error || 'Unknown Error',
          },
          index,
          channel,
        }),
      )
    }
  } catch (err: any) {
    console.log('err ====', err)
    yield put(
      deleteChannelFailure({
        error: {
          status: err.response?.status,
          message: err.response?.data?.message || 'Unknown Error',
        },
        index,
        channel,
      }),
    )
  }
}

function* setActiveChannelSaga(action) {
  // TODO 3023: Check this!
  // if (action.payload?.slug) {
  //   const pathname = `/space/${action.payload.slug}`
  //   if (location.pathname !== pathname) yield call(Router.push, pathname)
  // }
}

function* upsertChannelSaga(action) {
  // TODO: Refactor to separate create and update into 2 different saga functions
  try {
    const data = action.payload
    const membership = yield select(selectMembership)
    let result
    if (data.id) {
      result = yield call(updateChannelApi, data.id, data)
    } else {
      result = yield call(createChannelApi, data, membership.id)
    }

    if (result?.data?.success) {
      toast.success(`Space ${data.id ? 'updated' : 'created'} successfully`)
      yield put(upsertChannelSuccess(result.data.data))
    } else {
      toast.error(result?.data?.message || 'Failed to create space')
      yield put(
        upsertChannelFailure({
          status: 500,
          message: result?.error || 'Unknown Error',
        }),
      )
    }
  } catch (err: any) {
    toast.error(err.response?.data?.message || 'Failed to create space')
    yield put(
      upsertChannelFailure({
        status: err.response?.status,
        message: err.response?.data?.message || 'Unknown Error',
      }),
    )
  }
}

function* getEventsSaga(action) {
  try {
    console.log('PARAMS', action.payload.params)
    const result = yield call(getEventsApi, {
      membershipId: action.payload.membershipId,
      ...action.payload.params,
    })
    if (result?.data?.success) {
      yield put(
        getEventsSuccess({
          init: !action.payload?.skip,
          total: result.data.data?.total,
          docs: result.data.data?.docs || [],
        }),
      )
    } else {
      yield put(
        getEventsFailure({
          status: 500,
          message: result?.error || 'Unknown Error',
        }),
      )
    }
  } catch (err: any) {
    console.log('err ====', err)
    yield put(
      getEventsFailure({
        status: 500,
        message: err.message,
      }),
    )
  }
}

function* upsertEventSaga(action) {
  try {
    const { id, header_image_file, ...rest } = action.payload.data
    const { membershipId } = action.payload
    let result

    if (header_image_file) {
      const temp = yield call(uploadFileToCloudinaryApi, header_image_file, 'image')
      rest.header_image = temp?.data?.secure_url as string
    }

    if (id) {
      result = yield call(updateEventApi, membershipId, id, rest)
    } else {
      result = yield call(createEventApi, membershipId, rest)
    }

    if (result?.data?.success) {
      yield put(upsertEventSuccess(result.data.data))
    } else {
      yield put(
        upsertEventFailure({
          status: 500,
          message: result?.error || 'Unknown Error',
        }),
      )
    }
  } catch (err: any) {
    console.log('err ====', err)
    yield put(
      upsertEventFailure({
        status: 500,
        message: err.message,
      }),
    )
  }
}

function* deleteEventSaga(action) {
  try {
    const result = yield call(deleteEventApi, action.payload.id, action.payload.membershipId)
    if (result?.data?.success) {
      yield put(deleteEventSuccess(action.payload))
    } else {
      yield put(
        deleteEventFailure({
          error: {
            status: 500,
            message: result?.error || 'Unknown Error',
          },
          id: action.payload,
        }),
      )
    }
  } catch (err: any) {
    console.log('err ====', err)
    yield put(
      deleteEventFailure({
        error: {
          status: 500,
          message: err.message,
        },
        id: action.payload,
      }),
    )
  }
}

function* upsertFeedSaga(action) {
  try {
    const { id, ...rest } = action.payload.data
    const membershipId = action.payload.membershipId
    const result = id ? yield call(updateFeedApi, id, rest) : yield call(createFeedApi, rest, membershipId)
    if (result?.data?.success) {
      yield put(
        upsertFeedSuccess({
          data: result.data.data,
        }),
      )
      if (id) {
        toast.success(action.payload.messages?.success || 'Post updated')
      }
    } else {
      const message = action.payload.messages?.fail || (id ? 'Failed to update post' : 'Failed to create post')
      toast.error(message)
      yield put(
        upsertFeedFailure({
          status: 400,
          message: message,
        }),
      )
    }
  } catch (err: any) {
    toast.error(err.response?.data?.message || 'Failed to update post')
    yield put(
      upsertFeedFailure({
        status: err.response?.status,
        message: err.response?.data?.message,
      }),
    )
  }
}

function* reportFeedSaga(action) {
  try {
    const { id, ...rest } = action.payload.data
    const result = yield call(reportFeedApi, id, rest)
    if (result?.data?.success) {
      yield put(
        upsertFeedSuccess({
          data: result.data.data,
        }),
      )
      if (id) {
        toast.success(action.payload.messages?.success || 'Post updated')
      }
    } else {
      const message = action.payload.messages?.fail || (id ? 'Failed to update post' : 'Failed to create post')
      toast.error(message)
      yield put(
        upsertFeedFailure({
          status: 400,
          message: message,
        }),
      )
    }
  } catch (err: any) {
    toast.error(err.response?.data?.message || 'Failed to update post')
    yield put(
      upsertFeedFailure({
        status: err.response?.status,
        message: err.response?.data?.message,
      }),
    )
  }
}

function* deleteFeedSaga(action) {
  try {
    const result = yield call(deleteFeedApi, action.payload.id, action.payload.data?.space)
    if (result?.data?.success) {
      toast.success(`${action.payload.data.feed_type === 'comment' ? 'Comment' : 'Post'} successfully deleted!`)
      yield put(
        deleteFeedSuccess({
          data: action.payload.data,
        }),
      )
    }
  } catch (err: any) {
    toast.error(err.response?.data?.message || 'Failed to delete post', 'error')
    yield put(
      deleteFeedFailure({
        data: action.payload.data,
      }),
    )
  }
}

function* createLiveSaga(action) {
  try {
    const { announcementPostImageFile, ...rest } = action.payload
    if (typeof announcementPostImageFile?.name === 'string') {
      const result = yield call(uploadFileToCloudinaryApi, announcementPostImageFile)
      rest['announcement_post_image'] = result.data.secure_url as string
    }
    const result = yield call(createLiveApi, rest)
    if (result?.data?.success) {
      yield put(createLiveSuccess(result.data.data))
    } else {
      yield put(
        createLiveFailure({
          status: 500,
          message: result?.error || 'Unknown Error',
        }),
      )
    }
  } catch (err: any) {
    console.log('err ====', err)
    yield put(
      createLiveFailure({
        status: err.response?.status,
        message: err.response?.data?.message || 'Unknown Error',
      }),
    )
  }
}

function* updateLiveSaga(action) {
  try {
    const { announcementPostImageFile, ...rest } = action.payload.data
    if (typeof announcementPostImageFile?.name === 'string') {
      const result = yield call(uploadFileToCloudinaryApi, announcementPostImageFile)
      rest['announcement_post_image'] = result.data.secure_url as string
    }

    const result = yield call(updateLiveApi, action.payload.id, rest)
    if (result?.data?.success) {
      yield put(updateLiveSuccess(result.data.data))
    } else {
      yield put(
        updateLiveFailure({
          status: 500,
          message: result?.error || 'Unknown Error',
        }),
      )
    }
  } catch (err: any) {
    console.log('err ====', err)
    yield put(
      updateLiveFailure({
        status: err.response?.status,
        message: err.response?.data?.message || 'Unknown Error',
      }),
    )
  }
}

function* getLiveSaga(action) {
  try {
    const result = yield call(getLiveApi, action.payload.id)
    if (result?.data?.success) {
      yield put(getLiveSuccess(result.data.data))
    } else {
      yield put(
        getLiveFailure({
          status: 500,
          message: result?.error || 'Unknown Error',
        }),
      )
    }
  } catch (err: any) {
    console.log('err ====', err)
    yield put(
      getLiveFailure({
        status: err.response?.status,
        message: err.response?.data?.message || 'Unknown Error',
      }),
    )
  }
}

function* rootSaga() {
  yield all([
    takeLatest(getEvents, getEventsSaga),
    takeLatest(upsertEvent, upsertEventSaga),
    takeLatest(deleteEvent, deleteEventSaga),
    takeLatest(deleteFeed, deleteFeedSaga),
    takeLatest(upsertFeed, upsertFeedSaga),
    takeLatest(reportFeed, reportFeedSaga),
    takeLatest(getMembership, getMembershipSaga),

    takeLatest(getMembershipSuccess, resetSpaceSaga), // NOTE: Reset the state of spaces when membership is changed.

    takeLatest(getContentLibrary, getContentLibrarySaga),
    takeLatest(updateMembershipSetting, updateMembershipSettingSaga),
    takeLatest(getMembers, getMembersSaga),
    takeLatest(setActiveMember, getMemberSaga),

    takeLatest(disconnectStripeAccount, disconnectStripeAccountSaga),
    takeLatest(startMembership, startMembershipSaga),
    takeLatest(getActiveUser, getActiveUserSaga),
    takeLatest(updateUserProfile, updateUserProfileSaga),

    takeEvery(deleteChannel, deleteChannelSaga),
    takeLatest(upsertChannel, upsertChannelSaga),
    takeLatest(getChannels, getChannelsSaga),

    takeLatest(getLive, getLiveSaga),
    takeLatest(createLive, createLiveSaga),
    takeLatest(updateLive, updateLiveSaga),
  ])
}

export default rootSaga
