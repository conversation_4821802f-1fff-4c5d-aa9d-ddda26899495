import { createSlice, PayloadAction } from '@reduxjs/toolkit'

import { RootState } from '../store'

export interface LikesState {
  likesMap: { [feedId: string]: { liked: boolean; likeCount: number } } | {}
}

const initialState: LikesState = {
  likesMap: {},
}

export const likesSlice = createSlice({
  name: 'likesSlice',
  initialState,
  reducers: {
    addLikeData: (state, action: PayloadAction<{ feedId: string; likeCount: number; status: 'liked' | 'unliked' }>) => {
      const { feedId, likeCount, status } = action.payload
      state.likesMap[feedId] = {
        liked: status === 'liked' ? true : false,
        likeCount: likeCount || 0,
      }
    },
    clearLikes: (state) => {
      state.likesMap = {}
    },
  },
})

export const { addLikeData, clearLikes } = likesSlice.actions
export const selectLikes = (state: RootState) => state.likes.likesMap
export default likesSlice.reducer
