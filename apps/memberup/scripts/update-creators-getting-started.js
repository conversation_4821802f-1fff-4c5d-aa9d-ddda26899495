require('dotenv').config()

const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

const updateCreatorsGettingStarted = async () => {
  try {
    const owners = await prisma.user.findMany({
      where: {
        role: 'owner',
      },
      include: {
        profile: true,
      },
    })

    for (const owner of owners) {
      const creators = await prisma.user.findMany({
        where: {
          role: 'creator',
          membership_id: owner.membership_id,
        },
      })

      for (const creator of creators) {
        const result = await prisma.userProfile.update({
          where: {
            user_id: creator.id,
          },
          data: {
            getting_started: owner.profile.getting_started,
          },
        })
      }
    }
  } catch (e) {
    console.error(e)
    process.exit(1)
  } finally {
  }
}

updateCreatorsGettingStarted()
