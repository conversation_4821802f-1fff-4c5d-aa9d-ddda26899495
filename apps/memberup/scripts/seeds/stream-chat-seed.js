require('dotenv').config()

const { StreamChat } = require('stream-chat')

const GET_STREAM_APP_KEY = process.env.NEXT_PUBLIC_GET_STREAM_APP_KEY
const GET_STREAM_APP_SECRET = process.env.GET_STREAM_APP_SECRET

const customRoles = ['admin', 'creator', 'member']
// const scopes = [ '.app', 'commerce', 'gaming', 'livestream', 'messaging', 'team' ]

const upsertCustomRoles = async () => {
  try {
    const client = new StreamChat(GET_STREAM_APP_KEY, GET_STREAM_APP_SECRET, {
      timeout: 30000,
    })
    // await streamChatServerClient.updateAppSettings({
    //   multi_tenant_enabled: true,
    //   permission_version: 'v2',
    //   migrate_permissions_to_v2: true,
    // })
    const existingRoles = await client.listRoles()

    for (const customRole of customRoles) {
      if (existingRoles.roles.findIndex((r) => r.name === customRole) < 0) {
        await client.createRole(customRole)
      }
    }
  } catch (e) {
    console.error(e)
    process.exit(1)
  } finally {
  }
}

upsertCustomRoles()
