import { sumBy } from 'lodash'
import { z } from 'zod'

export const customErrorMap: z.ZodErrorMap = (issue, ctx) => {
  if (issue.code === 'too_small' && issue.type === 'string') {
    if (!ctx.data) {
      return { message: 'This field is required' }
    }

    return { message: `Enter at least ${issue.minimum} characters` }
  }

  return { message: ctx.defaultError }
}

const testPassword = (password: string) => ({
  hasNumber: /\d/.test(password),
  hasLowerCase: /[a-z]/.test(password),
  hasUpperCase: /[A-Z]/.test(password),
  hasNonAlphanumeric: /[^a-zA-Z0-9]/.test(password),
})

const passwordValidator = (password: string): boolean => {
  const { hasNumber, hasLowerCase, hasUpperCase, hasNonAlphanumeric } = testPassword(password)

  return sumBy([hasLowerCase, hasUpperCase, hasNumber, hasNonAlphanumeric]) >= 2
}

const passwordValidatorMessage = (password: string): object => {
  const { hasNumber, hasLowerCase, hasUpperCase, hasNonAlphanumeric } = testPassword(password)

  const messageParts = []

  if (!hasLowerCase && !hasUpperCase) {
    messageParts.push('regular characters')
  } else if (!hasLowerCase) {
    messageParts.push('a lowercase character')
  } else if (!hasUpperCase) {
    messageParts.push('an uppercase character')
  }

  if (!hasNumber) {
    messageParts.push('a number')
  }

  if (!hasNonAlphanumeric) {
    messageParts.push('a special character')
  }

  return {
    message: [
      'Try adding',
      messageParts
        .map((part, index) => {
          if (index === 0) return part

          if (index === messageParts.length - 1) {
            return ` or ${part}`
          }

          return `, ${part}`
        })
        .join(''),
      'to your password to make it stronger',
    ].join(' '),
  }
}

export const passwordSchema = z.string().min(8).refine(passwordValidator, passwordValidatorMessage)

export const passwordsMatch = (data: { password: string; confirmPassword: string }): boolean => {
  return data.password === data.confirmPassword
}

export const passwordsMatchMessage = {
  message: 'Password and confirmation must match',
  path: ['confirmPassword'] as (string | number)[],
}

export const slugRegex = /^[a-zA-Z0-9_-]+$/
