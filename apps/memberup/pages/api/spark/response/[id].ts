import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { deleteSparkResponseById, findSparkResponseById } from '@/shared-libs/prisma/spark-response'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler
  .use(authenticationMiddleware)
  .get(async (req, res) => {
    try {
      const {
        query: { id },
      } = req
      const result = await findSparkResponseById(id as string)
      return res.json({ success: true, data: result })
    } catch (err: any) {
      sentryCaptureException(err)
      res.status(400).json(errorHandler(err, 'SparkResponse'))
    }
  })
  // .put(async (req, res) => {
  //   try {
  //     const {
  //       query: { id },
  //       body,
  //     } = req
  //
  //     const result = await updateSparkResponse({
  //       where: { id: id as string },
  //       data: body,
  //     })
  //     return res.status(201).send({ success: true, data: result })
  //   } catch (err: any) {
  //     sentryCaptureException(err)
  //     return res.status(400).json(errorHandler(err, 'SparkResponse'))
  //   }
  // })
  .delete(async (req, res) => {
    try {
      const { id } = req.query
      const result = await deleteSparkResponseById(id as string)
      if (result?.id) {
        return res.status(200).send({ success: true, data: result })
      }
      res.status(400).json(errorHandler(result, 'SparkResponse'))
    } catch (err: any) {
      sentryCaptureException(err)
      res.status(400).json(errorHandler(err, 'SparkResponse'))
    }
  })

export default handler
