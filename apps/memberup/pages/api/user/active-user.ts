import * as Sentry from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { USER_STATUS_ENUM } from '@memberup/shared/src/types/enum'
import { getAuthenticatedUserData } from '@/lib/server/users'
import authenticationMiddleware from '@/memberup/middlewares/authentication'

const handler = nc<NextApiRequest, NextApiResponse>()

handler.use(authenticationMiddleware).get(async (req, res) => {
  try {
    const authenticatedUser = req['user']

    const data = await getAuthenticatedUserData(authenticatedUser.id)

    if (!data.user) {
      return res.status(400).end('Failed to get the active user.')
    }

    if (data.user.status === USER_STATUS_ENUM.banned) {
      return res.status(400).json({
        message: 'Your account has been banned from MemberUp platform.',
      })
    }

    return res.status(200).send({
      success: true,
      data,
    })
  } catch (err: any) {
    console.error(err)
    Sentry.captureException(err)
    return res.status(500).end()
  }
})
export default handler
