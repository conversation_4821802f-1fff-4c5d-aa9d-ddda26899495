import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { knockTriggerWorkflow } from '@memberup/shared/src/libs/knock'
import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import processLinks from '@memberup/shared/src/services/functions/link_parser'
import { KNOCK_WORKFLOW_ENUM, USER_ROLE_ENUM } from '@memberup/shared/src/types/enum'
import { getMuxAssets } from '@/memberup/libs/mux'
import { getAbsoluteCommunityURL, getCommunityBaseURL } from '@/memberup/libs/utils'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { deleteFeedByIdAndType, findFeedById, updateFeed } from '@/shared-libs/prisma/feed'
import prisma from '@/shared-libs/prisma/prisma'
import { getFullName } from '@/shared-libs/profile'
import { stripHtml } from '@/shared-libs/string-utils'

const handler = nc<NextApiRequest, NextApiResponse>()

handler
  .use(authenticationMiddleware)
  .put(async (req, res) => {
    try {
      const user = req['user']
      const {
        query: { id },
        body: { mentioned_users, ...data },
      } = req

      const oldFeed = await prisma.feed.findUnique({
        where: {
          id: id as string,
        },
        include: {
          channel: {
            include: {
              membership: true,
            },
          },
        },
      })

      const membership = oldFeed?.channel.membership

      if (!oldFeed) {
        return res.status(404).json({
          message: `Post not found.`,
        })
      }

      if (!user.role || user.role === USER_ROLE_ENUM.member) {
        if (oldFeed.user_id !== user.id) {
          return res.status(403).json({
            message: `You don't have the permission to update post.`,
          })
        }
      }

      if (data?.attachments?.length) {
        for (const attachment of data.attachments) {
          if (attachment.mux_upload_id) {
            const assets = await getMuxAssets({ upload_id: attachment.mux_upload_id })
            if (assets.length) {
              attachment.mux_asset = {
                id: assets[0].id,
                status: assets[0].status,
                // duration: assets[0].duration,
                aspect_ratio: assets[0].aspect_ratio,
                playback_ids: assets[0].playback_ids,
                passthrough: assets[0].passthrough,
                tracks: [],
              }
            }
          }
        }
      }

      if (mentioned_users) {
        if (!data['metadata']) {
          data['metadata'] = oldFeed.metadata || {}
        }
        data['metadata'].mentioned_users = mentioned_users
      }

      if (data.text) {
        const externalLinksMetadata = await processLinks(data.text)
        if (!data['metadata']) {
          data['metadata'] = oldFeed.metadata || {}
        }
        data['metadata'].links = externalLinksMetadata.links ?? undefined
      }

      // NOTE: We don't want to allow to change the channel for a post.
      delete data['channel_id']
      delete data['host']
      delete data['community_name']

      data['edited'] = true
      const { result, updatedStreamMessage } = await updateFeed({
        where: { id: id as string },
        data: data,
      })

      if (result?.id) {
        // Notify mentioned users.
        const actorName = getFullName(user.first_name, user.last_name)

        let messageContent = result.title
        const messageType = result.hierarchy_order ? 'comment' : 'post'

        let permalink = ''
        if (messageType === 'comment') {
          if (result.parent_id) {
            const parentPost = await findFeedById(result.parent_id)
            permalink = `/post/${parentPost.permalink}`
          }
          permalink += '?comment_id=' + result.id
          messageContent = stripHtml(result.text)
        } else {
          permalink = `/post/${result.permalink}`
        }

        const newMentionNotificationPayload = {
          actor_name: actorName,
          message_type: messageType,
          message_content: messageContent,
          url: permalink,
          community_name: membership.name,
          community_slug: getCommunityBaseURL(membership),
          email_post_url: `${getAbsoluteCommunityURL(req, membership)}${permalink}`,
        }
        const mentionedUsersIds = mentioned_users
          ?.map((user: { id: string }) => user.id)
          .filter((id: string) => id !== '-1')

        if (mentionedUsersIds.length > 0) {
          await knockTriggerWorkflow(
            KNOCK_WORKFLOW_ENUM.new_mention_notification,
            mentionedUsersIds,
            newMentionNotificationPayload,
            user.id,
            user.membership_id,
          )
        }
        return res.status(200).send({ success: true, data: updatedStreamMessage?.message })
      }
      res.status(400).json(errorHandler(result, 'Post'))
    } catch (error) {
      console.error(error)
      sentryCaptureException(error)
      res.status(500).end()
    }
  })
  .delete(async (req, res) => {
    try {
      const user = req['user']
      const { id } = req.query

      const feed = await findFeedById(id as string)
      if (!feed) {
        return res.status(404).json({
          message: `Post not found.`,
        })
      }

      // TODO: Check user permissions to delete the post.

      // if (!user.role || user.role === USER_ROLE_ENUM.member) {
      //   if (feed.user_id !== user.id) {
      //     return res.status(403).json({
      //       message: `You don't have the permission to delete post.`,
      //     })
      //   }
      // }

      await deleteFeedByIdAndType(feed.id, feed.feed_type, user.id)
      return res.status(200).send({ success: true })
    } catch (err: any) {
      console.error(err)
      sentryCaptureException(err)
      return res.status(500).json(errorHandler(err, 'Feed'))
    }
  })

export default handler
