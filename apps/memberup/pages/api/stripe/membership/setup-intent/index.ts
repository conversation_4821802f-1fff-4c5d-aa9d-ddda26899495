import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { status200, status400, status500 } from '@/shared-libs/api-utils'
import prisma from '@/shared-libs/prisma/prisma'
import { findUserMembershipByIds } from '@/shared-libs/prisma/user-membership'
import { getFullName } from '@/shared-libs/profile'
import { stripeCreateSetupIntent, stripeGetOrCreateCustomer } from '@/shared-libs/stripe'
import { TStripeConnectAccount } from '@/shared-types/types'

const handler = nc<NextApiRequest, NextApiResponse>()

handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const user = req['user']
    const { membership_id: membershipId } = req.query
    if (!membershipId) {
      return status400(res, 'membership_id is required.')
    }

    const userMembership = await findUserMembershipByIds(membershipId, user.id, 'payment_pending')
    if (!userMembership) {
      return status400(res, 'You need to first join this community.')
    }

    const membership = userMembership.membership
    const membershipSetting = userMembership.membership.membership_setting

    const connectedStripeAccountInfo = membershipSetting?.stripe_connect_account as TStripeConnectAccount
    const stripeUserId = membershipSetting?.stripe_connect_account['stripe_user_id']

    //Create the stripe customer for the membership
    let stripeCustomerId = userMembership.stripe_customer_id
    if (!stripeCustomerId) {
      const userFullName = getFullName(user.first_name, user.last_name, '')
      const params = {
        email: user.email,
        description: `${membership.name}:${userFullName}`,
        name: userFullName,
        metadata: {
          membership_id: membership.id,
          membership_name: membership.name,
        },
      }
      const stripeCustomer = await stripeGetOrCreateCustomer(connectedStripeAccountInfo, params)
      stripeCustomerId = stripeCustomer.id
      await prisma.userMembership.update({
        where: {
          id: userMembership.id,
        },
        data: {
          stripe_customer_id: stripeCustomer.id,
        },
      })
    }

    // Create the setup intent
    const setupIntent = await stripeCreateSetupIntent(
      connectedStripeAccountInfo,
      {
        customer: stripeCustomerId,
        payment_method_types: ['card'],
        usage: 'on_session',
      },
      stripeUserId,
    )
    return status200(res, setupIntent)
  } catch (err: any) {
    console.error(err)
    return status500(res, err.message)
  }
})

export default handler
