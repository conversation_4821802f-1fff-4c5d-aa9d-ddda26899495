import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { deleteEventAttendeeById } from '@memberup/shared/src/libs/prisma/event-attendee'
import authenticationMiddleware from '@/memberup/middlewares/authentication'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).delete(async (req, res) => {
  try {
    const { id } = req.query
    const result = await deleteEventAttendeeById(id as string)
    if (result?.id) {
      return res.status(200).send({ success: true, data: result })
    }
    res.status(500).json(errorHandler(result, 'EventAttendee'))
  } catch (err: any) {
    sentryCaptureException(err)
    res.status(500).json(errorHandler(err, 'EventAttendee'))
  }
})

export default handler
